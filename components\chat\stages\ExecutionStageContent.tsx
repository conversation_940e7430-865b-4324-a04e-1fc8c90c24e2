import React, { useMemo, useState } from "react";
import { Modal, Collapse, Tooltip } from "antd";
import GenerateTable from "../generate-table";
import { mapToolName } from "@/utils/mapToolName";

interface ExecutionStageContentProps {
  stageData: any;
  // 从外部传入一个工具序号，用于自动打开对应执行结果弹窗
  openToolIndex?: number | null;
  // 可选：从分析引用处传来的引用内容，在弹窗中展示
  openTip?: string | null;
}

const SectionTitle: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <h4 className="mb-2 text-sm font-semibold text-[#334155]">{children}</h4>;

const ExecutionStageContent: React.FC<ExecutionStageContentProps> = ({
  stageData,
  openToolIndex,
  openTip,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    data: any[];
    toolName: string;
    tip?: string;
  } | null>(null);

  // 知识库引用弹窗
  const [citeVisible, setCiteVisible] = useState(false);
  const [citeTitle, setCiteTitle] = useState<string>("");
  const [citeList, setCiteList] = useState<any[]>([]);

  if (!stageData) {
    return <div className="text-sm text-[#64748B]">无执行数据</div>;
  }

  const executions = stageData.tool_executions || [];

  if (executions.length === 0) {
    return <div className="text-sm text-[#64748B]">无执行记录</div>;
  }

  const handleShowTable = (execution: any, tip?: string) => {
    const rows = execution?.result?.retrieval_result || [];
    if (Array.isArray(rows) && rows.length > 0) {
      setModalData({
        title: execution.query || "执行结果",
        data: rows,
        toolName: execution.tool_name || "未知工具",
        tip,
      });
      setModalVisible(true);
    }
  };

  // 归一化提取 chunk 引用
  const getChunks = (execution: any): any[] => {
    const meta = execution?.metadata?.chunk_metadata;
    if (!meta) return [];
    if (Array.isArray(meta)) return meta;
    if (typeof meta === "object") return Object.values(meta);
    return [];
  };

  const handleShowCitations = (execution: any) => {
    const chunks = getChunks(execution);
    if (!chunks.length) return;
    setCiteTitle(execution?.query || "知识库引用");
    setCiteList(chunks);
    setCiteVisible(true);
  };

  // 当外部给定 openToolIndex 时，尝试打开对应执行结果
  React.useEffect(() => {
    if (openToolIndex === null || openToolIndex === undefined) return;
    const target = executions.find(
      (ex: any) => ex?.tool_index === openToolIndex
    );
    if (target) {
      handleShowTable(target, openTip || undefined);
    }
    // 仅在索引变化时尝试
  }, [openToolIndex]);

  // 分组：按工具名聚合
  const groupedExecutions = useMemo(() => {
    const acc: Record<string, any[]> = {};
    for (const ex of executions) {
      const name = (ex?.tool_name || "未知工具") as string;
      if (!acc[name]) acc[name] = [];
      acc[name].push(ex);
    }
    return acc;
  }, [executions]);

  // 每种工具不同的背景色
  const getToolBg = (toolName: string) => {
    const k = (toolName || "未知工具").toLowerCase();
    if (k.includes("nl2sql"))
      return "bg-[#EEF2FF] hover:bg-[#E0E7FF] border-[#C7D2FE]";
    if (k.includes("search"))
      return "bg-[#ECFDF5] hover:bg-[#D1FAE5] border-[#A7F3D0]";
    if (k.includes("crawler") || k.includes("crawl"))
      return "bg-[#FFFBEB] hover:bg-[#FEF3C7] border-[#FDE68A]";
    if (k.includes("excel") || k.includes("sheet"))
      return "bg-[#F0FDFA] hover:bg-[#CCFBF1] border-[#99F6E4]";
    if (k.includes("code"))
      return "bg-[#FAF5FF] hover:bg-[#F3E8FF] border-[#E9D5FF]";
    if (k.includes("query_knowledge_base") || k.includes("knowledge"))
      return "bg-[#ECFEFF] hover:bg-[#CFFAFE] border-[#A5F3FC]";
    return "bg-[#F8FAFC] hover:bg-[#F1F5F9] border-[#E2E8F0]";
  };

  return (
    <div className="space-y-4">
      <SectionTitle>执行记录 ({executions.length})</SectionTitle>

      {/* 折叠面板：按工具名分组（使用 items 以避免 rc-collapse 弃用警告） */}
      <Collapse
        bordered={false}
        defaultActiveKey={Object.keys(groupedExecutions).map((name) =>
          mapToolName(name)
        )}
        className="bg-white"
        items={Object.entries(groupedExecutions).map(([toolName, list]) => ({
          key: mapToolName(toolName),
          label: (
            <span className="flex items-center justify-between w-full">
              <span className="font-medium text-[#334155]">
                {mapToolName(toolName)}
              </span>
              <span className="text-xs text-[#64748B]">
                {list.length} 个问题
              </span>
            </span>
          ),
          children: (
            <div className="flex flex-wrap gap-2">
              {list.map((execution: any, idx: number) => {
                const rows = execution?.result?.retrieval_result || [];
                const hasTableData = Array.isArray(rows) && rows.length > 0;
                const bg = getToolBg(toolName);
                const isHighlighted =
                  typeof openToolIndex === "number" &&
                  execution?.tool_index === openToolIndex;
                const chunks = getChunks(execution);
                const content = (
                  <div
                    className={`border ${bg} rounded px-2 py-1 text-xs text-[#334155] ${
                      hasTableData ? "cursor-pointer" : "cursor-default"
                    } ${isHighlighted ? "ring-2 ring-[#6366F1]" : ""}`}
                    style={{ width: 100 }}
                    onClick={() => hasTableData && handleShowTable(execution)}
                  >
                    <div
                      className="font-mono whitespace-nowrap overflow-hidden text-ellipsis"
                      title=""
                    >
                      {execution?.query || "（无问题）"}
                    </div>
                    {hasTableData && (
                      <div className="mt-1 text-[10px] text-[#6366F1]">
                        查看结果 →
                      </div>
                    )}
                  </div>
                );

                return (
                  <Tooltip
                    key={`${toolName}-${idx}`}
                    title={execution?.query}
                    mouseEnterDelay={0.2}
                  >
                    <div className="flex flex-col gap-1 items-start">
                      {content}
                      {chunks.length > 0 && (
                        <span
                          className="inline-flex items-center gap-1 text-xs text-gray-500 hover:text-gray-700 cursor-pointer transition"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShowCitations(execution);
                          }}
                        >
                          <svg
                            className="w-3 h-3 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          {chunks.length} 条参考
                        </span>
                      )}
                    </div>
                  </Tooltip>
                );
              })}
            </div>
          ),
        }))}
      />

      {/* 表格弹窗 */}
      <Modal
        title={
          <div>
            <div className="font-medium">{modalData?.toolName}</div>
            <div className="text-sm text-gray-500 font-normal mt-1">
              {modalData?.title}
            </div>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width="90%"
        style={{ maxWidth: "1200px" }}
        styles={{
          body: { maxHeight: "70vh", overflow: "auto" },
        }}
      >
        {modalData?.data && <GenerateTable dataSource={modalData.data} />}
        {modalData?.tip && (
          <div className="mt-2 text-xs text-gray-600">
            <span className="font-medium text-gray-700">证据来源：</span>
            {modalData.tip}
          </div>
        )}
      </Modal>

      {/* 知识库引用弹窗 */}
      <Modal
        title={
          <div>
            <div className="font-medium">知识库引用</div>
            <div className="text-sm text-gray-500 font-normal mt-1">
              {citeTitle}
            </div>
          </div>
        }
        open={citeVisible}
        onCancel={() => setCiteVisible(false)}
        footer={null}
        width="80%"
        style={{ maxWidth: "1000px" }}
        styles={{
          body: { maxHeight: "70vh", overflow: "auto" },
        }}
      >
        <div className="space-y-3">
          {citeList.map((item: any, i: number) => {
            const fileName = item?.file_name || "未知文件";
            const filePath = item?.file_path as string | undefined;
            const size = item?.file_size;
            const fmt = item?.file_format;
            const created = item?.created_time;
            const modified = item?.modified_time;
            const content = item?.content_text as string | undefined;
            return (
              <div key={i} className="border rounded-md p-3 bg-[#F8FAFC]">
                <div className="flex items-center justify-between gap-2">
                  <div className="font-medium text-[#334155] truncate">
                    {filePath ? (
                      <a
                        href={filePath}
                        target="_blank"
                        rel="noreferrer"
                        className="hover:underline text-[#2563EB]"
                      >
                        {fileName}
                      </a>
                    ) : (
                      fileName
                    )}
                    {fmt && (
                      <span className="ml-2 text-xs text-[#64748B]">
                        [{fmt}]
                      </span>
                    )}
                  </div>
                  {filePath && (
                    <a
                      href={filePath}
                      target="_blank"
                      rel="noreferrer"
                      className="text-xs px-2 py-1 rounded border text-[#2563EB] border-[#93C5FD] hover:bg-[#EFF6FF]"
                    >
                      打开/下载
                    </a>
                  )}
                </div>
                <div className="mt-2 text-xs text-[#64748B] flex flex-wrap gap-3">
                  {typeof size === "number" && <span>大小：{size}B</span>}
                  {created && <span>创建：{created}</span>}
                  {modified && <span>修改：{modified}</span>}
                  {typeof item?.chunk_order === "number" && (
                    <span>片段序号：{item.chunk_order}</span>
                  )}
                  {typeof item?.word_count === "number" && (
                    <span>词数：{item.word_count}</span>
                  )}
                </div>
                {content && (
                  <div className="mt-3 text-sm text-[#111827] bg-white border-l-4 border-[#60A5FA] p-3 rounded">
                    <div className="text-[#2563EB] mb-1">引用片段</div>
                    <pre className="whitespace-pre-wrap text-xs leading-5">
                      {content}
                    </pre>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </Modal>
    </div>
  );
};

export default ExecutionStageContent;

'use client'

import React, { useState, useEffect, useRef, useCallback } from "react"
import { Document, Page, pdfjs } from "react-pdf"
import { debounce } from "lodash"

import {
  LuChevronLeft,
  LuChevronRight,
  LuZoomIn,
  LuZoomOut,
  LuMaximize,
  LuMinimize,
  LuDownload,
  LuRotateCw,
  LuSearch,
  LuChrome,
  LuFileText,
} from "react-icons/lu"

// 设置 worker
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.mjs'

interface PDFViewerProps {
  reference: {
    source: string
    page?: number
    page_label?: string
    total_pages?: number
    fragment?: string
    [key: string]: any
  }
  customStyles?: {
    primaryColor?: string
    backgroundColor?: string
    toolbarHeight?: string
  }
}

export const PDFViewer: React.FC<PDFViewerProps> = ({
  reference,
  customStyles = {},
}) => {
  const [currentPage, setCurrentPage] = useState(reference.page || 1)
  const [totalPages, setTotalPages] = useState(reference.total_pages || 1)
  const [scale, setScale] = useState(1)
  const [autoScale, setAutoScale] = useState(true)
  const [rotation, setRotation] = useState(0)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isReadyForDisplay, setIsReadyForDisplay] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [showSearch, setShowSearch] = useState(false)
  const [pageSize, setPageSize] = useState({ width: 0, height: 0 })

  const containerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const pdfWrapperRef = useRef<HTMLDivElement>(null)

  const {
    primaryColor = "#3b82f6",
    backgroundColor = "#f8fafc",
    toolbarHeight = "60px",
  } = customStyles

  const targetPage = reference.page || 1

  // 页面跳转
  const goToPage = (page: number) => {
    const newPage = Math.max(1, Math.min(totalPages, page))
    setCurrentPage(newPage)
  }

  const previousPage = () => goToPage(currentPage - 1)
  const nextPage = () => goToPage(currentPage + 1)
  const goToTargetPage = () => goToPage(targetPage)

  const zoomIn = () => {
    setAutoScale(false)
    setScale((prev) => Math.min(3, prev + 0.1))
  }

  const zoomOut = () => {
    setAutoScale(false)
    setScale((prev) => Math.max(0.5, prev - 0.1))
  }

  const resetZoom = () => {
    setAutoScale(true)
    // calculateScale will be triggered by useEffect
  }

  const rotate = () => setRotation((prev) => (prev + 90) % 360)

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const downloadPDF = () => {
    const link = document.createElement("a")
    link.href = reference.source
    link.download = "document.pdf"
    link.click()
  }

  const handleSearch = (e: React.KeyboardEvent | React.FormEvent) => {
    e.preventDefault()
    console.log("Searching for:", searchTerm)
  }

  // 计算适合容器的缩放比例
  const calculateScale = useCallback(() => {
    if (!contentRef.current || !pageSize.width || !autoScale) return

    const contentWidth = contentRef.current.clientWidth - 40 // 减去内边距

    if (contentWidth <= 0) return;

    // 只考虑横向缩放比例
    const scaleX = contentWidth / pageSize.width

    // 使用横向缩放比例，但限制最大值
    const newScale = Math.min(scaleX, 1.5) // 限制最大缩放比例为1.5
    setScale(newScale * 0.98) // 稍微缩小一点，确保横向不出现滚动条
    setIsReadyForDisplay(true)
  }, [autoScale, pageSize.width])

  // 处理页面尺寸变化
  const handlePageLoadSuccess = useCallback((page: any) => {
    const newPageSize = {
      width: page.originalWidth,
      height: page.originalHeight
    };
    
    // 只有当页面尺寸真正变化时才更新状态
    if (newPageSize.width !== pageSize.width || newPageSize.height !== pageSize.height) {
      setPageSize(newPageSize);
    }
  }, [pageSize.width, pageSize.height]);

  // 居中显示PDF，确保在放大时可以看到左侧内容
  const centerPDF = useCallback(() => {
    if (!pdfWrapperRef.current || !contentRef.current || !scale || autoScale) return;

    // 获取PDF容器宽度和内容区域宽度
    const pdfWidth = pdfWrapperRef.current.offsetWidth;
    const contentWidth = contentRef.current.clientWidth;

    // 如果PDF宽度超过容器宽度，确保可以水平滚动
    if (pdfWidth > contentWidth) {
      // 计算左侧边距，使PDF居中
      const marginLeft = Math.max(0, (contentWidth - pdfWidth) / 2);
      pdfWrapperRef.current.style.marginLeft = `${marginLeft}px`;

      // 确保内容区域可以滚动到最左侧
      if (scale > 1) {
        contentRef.current.scrollLeft = 0;
      }
    } else {
      pdfWrapperRef.current.style.marginLeft = '0px';
    }
  }, [autoScale, scale])

  useEffect(() => {
    goToPage(targetPage)
  }, [targetPage])

  // 监听容器大小变化并重新计算缩放
  useEffect(() => {
    const element = contentRef.current;
    if (!element) return;

    // 使用 lodash 的 debounce 函数
    const handleResize = debounce(() => {
      if (autoScale) {
        calculateScale();
      } else {
        centerPDF();
      }
    }, 200); // 200ms的防抖延迟

    const observer = new ResizeObserver(handleResize);
    observer.observe(element);

    // 初始计算一次
    handleResize();

    return () => {
      observer.disconnect();
      // 取消防抖函数
      handleResize.cancel();
    };
  }, [autoScale, calculateScale, centerPDF]);

  return (
    <div
      className={`flex flex-col w-full h-full bg-gray-100 rounded-lg overflow-hidden shadow-lg ${isFullscreen ? "fixed inset-0 z-50" : ""
        }`}
      ref={containerRef}
    >
      {/* 工具栏 - 固定在顶部 */}
      <div
        className="flex w-full overflow-auto items-center justify-between px-4 py-2 border-b border-gray-200 sticky top-0 z-10"
        style={{
          backgroundColor: backgroundColor,
          height: toolbarHeight,
        }}
      >
        {/* 左侧控制 */}
        <div className="flex items-center space-x-2">
          {/* 页面导航 */}
          <div className="flex items-center space-x-1 flex-shrink-0">
            <button
              onClick={previousPage}
              disabled={currentPage <= 1}
              className="p-2 rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors"
              title="上一页"
            >
              <LuChevronLeft size={18} />
            </button>

            <div className="flex items-center bg-white rounded-lg border px-2 py-1">
              <input
                type="number"
                value={currentPage}
                onChange={(e) => goToPage(parseInt(e.target.value) || 1)}
                className="w-14 text-center text-sm border-none outline-none px-2"
                min="1"
                max={totalPages}
              />
              <span className="text-sm text-gray-500 px-2">/ {totalPages}</span>
            </div>

            <button
              onClick={nextPage}
              disabled={currentPage >= totalPages}
              className="p-2 rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors"
              title="下一页"
            >
              <LuChevronRight size={18} />
            </button>
          </div>

          {/* 跳转按钮 */}
          {reference.page && currentPage !== targetPage && (
            <button
              onClick={goToTargetPage}
              className="px-3 py-1 flex items-center flex-shrink-0 text-sm rounded-lg text-white transition-colors hover:opacity-90"
              style={{ backgroundColor: primaryColor }}
              title={`跳转到第${targetPage}页`}
            >
              <LuChrome size={14} className="lg:inline mr-1 hidden" />
              第{targetPage}页
            </button>
          )}
        </div>

        {/* 右侧控制 */}
        <div className="flex items-center space-x-2 lg:ml-0 ml-5">
          {/* 缩放 */}
          <div className="flex items-center space-x-1">
            <button
              onClick={zoomOut}
              className="p-2 rounded-lg hover:bg-gray-200 transition-colors"
              title="缩小"
            >
              <LuZoomOut size={18} />
            </button>
            <span className="text-sm text-gray-600 min-w-12 text-center">
              {Math.round(scale * 100)}%
            </span>
            <button
              onClick={zoomIn}
              className="p-2 rounded-lg hover:bg-gray-200 transition-colors"
              title="放大"
            >
              <LuZoomIn size={18} />
            </button>
            <button
              onClick={resetZoom}
              className={`text-xs flex-shrink-0 px-2 py-1 rounded ${autoScale ? 'bg-blue-200 text-blue-700' : 'bg-gray-200 hover:bg-gray-300'}`}
              title="适应大小"
            >
              适应
            </button>
          </div>

          {/* 下载、全屏 */}
          <button
            onClick={downloadPDF}
            className="p-2 rounded-lg hover:bg-gray-200"
            title="下载"
          >
            <LuDownload size={18} />
          </button>
          <button
            onClick={toggleFullscreen}
            className="p-2 rounded-lg hover:bg-gray-200"
            title={isFullscreen ? "退出全屏" : "全屏"}
          >
            {isFullscreen ? <LuMinimize size={18} /> : <LuMaximize size={18} />}
          </button>
        </div>
      </div>

      {/* 内容区域 - 独立滚动区域，允许水平和垂直滚动 */}
      <div
        ref={contentRef}
        className="flex-1 overflow-auto bg-gray-50 p-4 w-full"
        style={{
          height: `calc(100% - ${toolbarHeight} - 30px)`, // 减去工具栏和页脚的高度
          position: 'relative'
        }}
      >
        {/* 使用一个额外的容器来确保PDF可以水平滚动 */}
        <div
          ref={pdfWrapperRef}
          className="pdf-wrapper"
          style={{
            display: 'inline-block',
            position: 'relative',
            minWidth: '100%',
            textAlign: 'center'
          }}
        >
          {/* 显示 loading 遮罩 */}
          {(isLoading || (autoScale && !isReadyForDisplay)) && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-2 border-gray-300 border-t-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">正在加载PDF...</p>
              </div>
            </div>
          )}

          {/* 实际 PDF 渲染 */}
          <Document
            file={reference.source}
            onLoadSuccess={useCallback(({ numPages }: { numPages: number }) => {
              if (totalPages !== numPages) {
                setTotalPages(numPages);
              }
              
              const targetPageNumber = reference.page || 1;
              if (currentPage !== targetPageNumber) {
                setCurrentPage(targetPageNumber);
              }
              
              setIsLoading(false);
            }, [totalPages, currentPage, reference.page])}
            loading={null} // 可选：防止 react-pdf 自己再显示 loading
          >
            <Page
              pageNumber={currentPage}
              scale={scale}
              rotate={rotation}
              renderAnnotationLayer={false}
              renderTextLayer={false}
              onLoadSuccess={handlePageLoadSuccess}
            />
          </Document>
        </div>
      </div>

      {/* 页脚 */}
      <div className="text-center text-xs text-gray-400 p-2 border-t">
        当前页码: {currentPage} / 共 {totalPages} 页
      </div>
    </div>
  )
}
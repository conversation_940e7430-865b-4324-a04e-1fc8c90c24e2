'use client'

import React, { useEffect } from 'react';
import { message } from 'antd';
import { apiAuth } from '@/lib/feishu';
import { useRouter } from 'next/navigation';

const Auth = () => {
  const router = useRouter();

  useEffect(() => {
    const handleAuth = async () => {
      try {
        const isAuthed = await apiAuth();
        if (isAuthed) {
          router.push('/chat');
        } else {
          message.error('飞书授权失败，请重试');
        }
      } catch (error) {
        console.error('Auth error:', error);
        message.error('授权过程中发生错误');
      }
    };

    handleAuth();
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="p-8 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold mb-4">飞书授权登录</h1>
        <p className="mb-6">请点击下方按钮完成飞书授权</p>
        <button 
          onClick={() => apiAuth()}
          className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
        >
          授权登录
        </button>
      </div>
    </div>
  );
}

export default Auth;

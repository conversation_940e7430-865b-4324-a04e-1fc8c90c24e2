'use client'

import React from 'react';
import { Popover } from 'antd';
import useIsMobile from '@/hooks/useIsMobile';

interface CitationReferenceProps {
  index: number;
  metadata: {
    title?: string;
    url?: string;
    favicon?: string;
    context?: string;
    order: number;
  };
}

export const CitationReference = ({ index, metadata }: CitationReferenceProps) => {
  const isMobile = useIsMobile()
  if (!metadata) return null;

  const content = (
    <div className="flex flex-col gap-3 max-w-[40vw] max-h-[35vh] overflow-y-auto p-1">
      {metadata.title && (
        <div className="font-medium text-gray-900 text-base">{metadata.title}</div>
      )}
      {metadata.context && (
        <div className="text-sm text-gray-600 line-clamp-5">{metadata.context}</div>
      )}
      {metadata.url && (
        <a
          href={metadata.url}
          target="_blank"
          rel="noopener noreferrer"
          className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-2 mt-1 border-t pt-2 border-gray-100"
        >
          {metadata.favicon && (
            <img src={metadata.favicon} alt="" className="w-5 h-5 flex-shrink-0" />
          )}
          <span className="truncate">{metadata.url}</span>
        </a>
      )}
    </div>
  );

  return (
    <Popover
      content={content}
      title={null}
      placement="top"
      trigger={isMobile ? 'click' : 'hover'}
      arrow={{ pointAtCenter: true }}
      // 使用 styles 替代 overlayStyle
      styles={{
        body: { maxWidth: '450px' }
      }}
      // 使用 classNames 替代 overlayClassName
      classNames={{
        root: 'citation-reference-popover'
      }}
    >
      <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-blue-100 text-blue-600 cursor-pointer hover:bg-blue-200 transition-colors mx-1">
        <a rel="noopener noreferrer">{index + 1}</a>
      </span>
    </Popover>
  );
};
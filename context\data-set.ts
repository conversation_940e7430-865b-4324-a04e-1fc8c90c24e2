import {
  createContext,
  useContext,
} from 'use-context-selector'

export type DataSet = {
  dataset_id: number
  dataset_name: string
}

type DataSetContext = {
  currentDataset: DataSet
  changeDataset: (id: number) => void
  datasetList: DataSet[]
  questionList: { id: number, question: string }[]
  refreshQuestionList: () => void
}

const DataSetContext = createContext<DataSetContext>({
  currentDataset: { dataset_id: 0, dataset_name: '智能选择' },
  changeDataset: (id: number) => { },
  datasetList: [],
  questionList: [],
  refreshQuestionList: () => { },
})

export const useDataSet = () => useContext(DataSetContext)

export default DataSetContext
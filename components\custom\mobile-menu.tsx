import React, { useState } from 'react';
import Siderbar from './siderbar';
import { useConversionStore } from '@/store/useConversion';
import { shallow } from 'zustand/shallow';

const MobileMenu = () => {
  const { isMobileCollapsed, setIsMobileCollapsed } = useConversionStore((state) => ({
    isMobileCollapsed: state.isMobileCollapsed,
    setIsMobileCollapsed: state.setIsMobileCollapsed
  }), shallow)
  
  return (
    <>
      <div
        onClick={() => setIsMobileCollapsed(!isMobileCollapsed)}
        className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${!isMobileCollapsed ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
      ></div>

      <div
        className={`w-[58%] h-svh fixed top-0 left-0 overflow-hidden bg-white z-50 transform transition-transform duration-300 ${!isMobileCollapsed ? 'translate-x-0' : '-translate-x-full'
          }`}
      >
        <Siderbar />
      </div>
    </>
  );
}

export default MobileMenu;

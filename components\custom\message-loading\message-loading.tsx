import React from 'react';
import './index.css';
import { cn } from '@/lib/utils';

const MessageLoading = ({ className }: { className?: string }) => {
  return (
    <div className={cn('flex items-center h-10', className)}>
      <span>正在查询中</span>
      <div className="loading-dots">
        <span className="dot">.</span>
        <span className="dot">.</span>
        <span className="dot">.</span>
      </div>
    </div>
  );
}

// const MessageLoading = ({ className }: { className?: string }) => {
//   return (
//     <div className={cn('h-10 w-10 flex items-center justify-center',className)}>
//       <div className="dot-spinner">
//         <div className="dot-spinner__dot"></div>
//         <div className="dot-spinner__dot"></div>
//         <div className="dot-spinner__dot"></div>
//         <div className="dot-spinner__dot"></div>
//         <div className="dot-spinner__dot"></div>
//         <div className="dot-spinner__dot"></div>
//         <div className="dot-spinner__dot"></div>
//         <div className="dot-spinner__dot"></div>
//       </div>
//     </div>
//   );
// }

export default MessageLoading;

import { useReferenceStore } from "@/store/useReference";
import { useSidePanelStore } from '@/store/useSidePanel'
import { IoMdClose } from "react-icons/io";
import { VideoPlayer } from "./video-player";
import { PDFViewer } from "./pdf-viewer";
import { AudioPlayer } from "./audio-player";

export const Reference = () => {
  const { referenceMap, currentReference } = useReferenceStore();

  // 这里只展示最后一个被添加的引用（你可以根据需求改成展示全部）
  const lastReference = Array.from(referenceMap.values()).at(-1);

  if (!currentReference) return <div className="p-4 text-gray-400">暂无引用内容</div>;

  return (
    <div className="w-full flex flex-col h-full">
      <div className="w-full h-full overflow-y-auto p-4 flex-1 box-border flex flex-col items-center justify-center">
        {currentReference.type === 'pdf' && <PDFViewer reference={currentReference} />}
        {currentReference.type === "video" && <VideoPlayer reference={currentReference} />}
        {currentReference.type === "audio" && <AudioPlayer reference={currentReference} />}
        {currentReference.type === "image" && <img className="w-full h-full object-cover" src={currentReference.source} alt="Reference" />}
      </div>
    </div>
  );
};
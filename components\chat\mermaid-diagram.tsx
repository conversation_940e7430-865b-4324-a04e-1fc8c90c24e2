'use client'

import React, { Fragment, useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';

interface MermaidDiagramProps {
  chart: string;
}

const MermaidDiagram = ({ chart }: MermaidDiagramProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const [id] = useState(() => `mermaid-diagram-${Math.random().toString(36).substring(2, 9)}`);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalSvgContent, setModalSvgContent] = useState<string>('');
  const [scale, setScale] = useState(1);
  const [modalRenderKey, setModalRenderKey] = useState(0);
  const [isModalLoading, setIsModalLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'fit' | 'actual'>('fit'); // 'fit' 适应窗口, 'actual' 实际大小

  useEffect(() => {
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    });

    const renderChart = async () => {
      if (chart && ref.current) {
        try {
          const { svg } = await mermaid.render(id, chart);
          if (ref.current) {
            ref.current.innerHTML = svg;
          }
        } catch (error) {
          console.error("Mermaid rendering error:", error);
          if (ref.current) {
            ref.current.innerHTML = 'Error: Could not render diagram.';
          }
        }
      }
    };

    renderChart();
  }, [chart, id]);

  // 为模态框渲染更大的图表
  useEffect(() => {
    if (isModalOpen && chart) {
      const renderModalChart = async () => {
        setIsModalLoading(true);
        try {
          // 生成唯一的模态框 ID，避免冲突
          const uniqueModalId = `mermaid-modal-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

          // 为模态框使用更大的配置
          mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            themeVariables: {
              fontSize: '16px',
              fontFamily: '"trebuchet ms", verdana, arial, sans-serif',
            }
          });

          const { svg } = await mermaid.render(uniqueModalId, chart);
          setModalSvgContent(svg);
        } catch (error) {
          console.error("Modal Mermaid rendering error:", error);
          // 如果渲染失败，尝试清理并重新渲染
          try {
            const fallbackId = `mermaid-fallback-${Date.now()}`;
            const { svg } = await mermaid.render(fallbackId, chart);
            setModalSvgContent(svg);
          } catch (fallbackError) {
            console.error("Fallback rendering also failed:", fallbackError);
            setModalSvgContent('<div style="padding: 20px; text-align: center; color: #666;">Error: Could not render diagram.</div>');
          }
        } finally {
          setIsModalLoading(false);
        }
      };

      // 添加小延迟确保模态框完全打开
      const timer = setTimeout(renderModalChart, 100);
      return () => clearTimeout(timer);
    }
  }, [isModalOpen, chart, modalRenderKey]);

  const handleClick = () => {
    setIsModalOpen(true);
    setScale(1); // 重置缩放
    setViewMode('fit'); // 默认适应窗口
    setModalRenderKey(prev => prev + 1); // 强制重新渲染
    setModalSvgContent(''); // 清空之前的内容
    setIsModalLoading(true); // 设置加载状态
  };

  const handleCloseModal = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setIsModalOpen(false);
      // 清理模态框内容
      setModalSvgContent('');
    }
  };

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.2, 3));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.2, 0.5));
  };

  const handleResetZoom = () => {
    setScale(1);
  };

  const handleFitToWindow = () => {
    setViewMode('fit');
    setScale(1);
  };

  const handleActualSize = () => {
    setViewMode('actual');
    setScale(1);
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsModalOpen(false);
    } else if (e.key === '+' || e.key === '=') {
      handleZoomIn();
    } else if (e.key === '-') {
      handleZoomOut();
    } else if (e.key === '0') {
      handleResetZoom();
    } else if (e.key === 'f' || e.key === 'F') {
      handleFitToWindow();
    } else if (e.key === 'a' || e.key === 'A') {
      handleActualSize();
    }
  };

  useEffect(() => {
    if (isModalOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isModalOpen]);

  return (
    <>
      <div
        className="mermaid-container"
        style={{
          overflowX: 'auto',
          whiteSpace: 'nowrap',
          cursor: 'pointer',
        }}
        onClick={handleClick}
        title="点击查看大图"
      >
        <div
          ref={ref}
          style={{
            display: 'inline-block',
            minWidth: '100%',
          }}
        />
        <div className="mermaid-preview-hint">
          🔍 点击放大
        </div>
      </div>

      {isModalOpen && (
        <div className="mermaid-modal" onClick={handleCloseModal}>
          <div className="mermaid-modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="mermaid-modal-header">
              <div className="mermaid-view-controls">
                <button
                  onClick={handleFitToWindow}
                  className={viewMode === 'fit' ? 'active' : ''}
                  title="适应窗口 (F)"
                >
                  📐
                </button>
                <button
                  onClick={handleActualSize}
                  className={viewMode === 'actual' ? 'active' : ''}
                  title="实际大小 (A)"
                >
                  🔍
                </button>
                {
                  viewMode === 'actual' &&
                  <Fragment>
                    <div className="divider"></div>
                    <button onClick={handleZoomOut} title="缩小 (-)">−</button>
                    <span>{Math.round(scale * 100)}%</span>
                    <button onClick={handleZoomIn} title="放大 (+)">+</button>
                    <button onClick={handleResetZoom} title="重置 (0)">⌂</button>
                  </Fragment>
                }
              </div>
              <button
                className="mermaid-modal-close"
                onClick={() => {
                  setIsModalOpen(false);
                  setModalSvgContent('');
                }}
                title="关闭 (ESC)"
              >
                ×
              </button>
            </div>
            <div className="mermaid-modal-body">
              {isModalLoading ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: '200px',
                  color: '#666',
                  fontSize: '16px'
                }}>
                  <div>
                    <div style={{ marginBottom: '10px', textAlign: 'center' }}>🔄</div>
                    正在渲染图表...
                  </div>
                </div>
              ) : (
                <div
                  ref={modalRef}
                  dangerouslySetInnerHTML={{ __html: modalSvgContent }}
                  className={`mermaid-diagram-content ${viewMode}`}
                  style={{
                    transform: viewMode === 'actual' ? `scale(${scale})` : undefined,
                    transformOrigin: 'center center',
                    transition: 'transform 0.2s ease',
                  }}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MermaidDiagram;
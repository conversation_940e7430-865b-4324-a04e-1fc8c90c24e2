import { request } from '@/services/base'

enum API {
  GET_CONVERSATION_LIST = '/chat/conversations',
  GET_MESSAGES_BY_CONVERSION_ID = '/chat/conversation/${conversation_id}',
  UPDATE_CONVERSATION = '/chat/conversation/${conversation_id}/name',
  DELETE_CONVERSATION = '/chat/conversation/${conversation_id}',
  FEEDBACK_MESSAGE = '/chat/message/${message_id}/feedbacks',
  SUGGESTION = '/chat/suggestion',
  DATASETLIST = '/chat/suggestion',
  SUGGESTION_BY_DATASET = '/chat/questions/by-dataset',
  STOP_CHAT_MESSAGE = '/chat/chat-message/{task_id}/stop',
  // 深度研究报告（非流式）
  DEEP_RESEARCH_REPORT = '/chat/get_report',
  // 生成报告（普通接口），完成后将 tocUrl 写入缓存
  GENERATE_HTML_REPORT = '/chat/deepResearchReport',

  // 认证
  GET_APP_ID = '/auth/get-appid',
  AUTH_CODE = '/auth/auth',

  // 通用
  GET_CONFIG = '/config'
}

export const fetchConversationList = async (params: any) => {
  const data = await request.get(`${API.GET_CONVERSATION_LIST}`, { ...params })
  return data
}

export const fetchMessagesByConversionId = async (id: string) => {
  const data = await request.get(API.GET_MESSAGES_BY_CONVERSION_ID.replace('${conversation_id}', id))
  return data
}

export const updateConversation = async (id: string, name: string) => {
  const data = await request.post(API.UPDATE_CONVERSATION.replace('${conversation_id}', id), {
    name,
  })
  return data
}

export const deleteConversation = async (id: string) => {
  const data = await request.delete(API.DELETE_CONVERSATION.replace('${conversation_id}', id))
  return data
}

export const feedbackMessage = async (id: string, feedback: number) => {
  const data = await request.post(API.FEEDBACK_MESSAGE.replace('${message_id}', id), {
    feedback,
  })
  return data
}

export const fetchSuggestion = async () => {
  const data = await request.get(API.SUGGESTION)
  return data
}


export const fetchDataSetList = async () => {
  const data = await request.get(API.DATASETLIST)
  return data
}

export const fetchSuggestionByDataset = async (params: any) => {
  const data = await request.get(API.SUGGESTION_BY_DATASET, { ...params })
  return data
}

export const fetchGetConfig = async () => {
  const data = await request.get(API.GET_APP_ID)
  return data
}

export const fetchAuthCode = async (code: string, source: string, username?: string, password?: string) => {
  const data = await request.get(`${API.AUTH_CODE}`, { code, source, username, password })
  return data
}

export const fetchStopChatMessage = async (task_id: string) => {
  const data = await request.post(API.STOP_CHAT_MESSAGE.replace('{task_id}', task_id))
  return data
}

export const fetchGetCommonConfig = async () => {
  const data = await request.get(API.GET_CONFIG)
  return data
}

// 生成深度研究报告（普通接口，不使用SSE）
// 入参需要 message_id 与 conversation_id，返回包含 toc_url 的数据
export const fetchDeepResearchReport = async (params: { message_id: string; conversation_id: string }) => {
  const data = await request.post(API.DEEP_RESEARCH_REPORT, params)
  return data
}

// 生成报告 HTML（普通接口，不使用SSE）
export const fetchGenerateHtmlReport = async (params: { message_id: string; conversation_id: string }) => {
  const data = await request.post(API.GENERATE_HTML_REPORT, params)
  return data
}

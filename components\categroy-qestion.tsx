"use client";

import React, { useState } from "react";
import { AiOutlineRight, AiOutlineDown } from "react-icons/ai";
import { useConversionStore } from "@/store/useConversion";
import { useTranslation } from "react-i18next";

// 图标与颜色池
const iconColors = [
  "#DD4265",
  "#54A4DB",
  "#F7D367",
  "#0000F5",
  "#F1D478",
  "#794AD0",
  "#F5A623",
];
const icons = ["🔥", "⭐", "🚀", "📘", "🎧", "📄", "🧠"];

// 数据结构
const data = {
  categories: [
    {
      type: "deep",
      category: "深度问答",
      subCategories: [
        {
          subCategory: "客户反馈",
          questions: [
            "系统A在2024年Q2的客户负面反馈是否显著上升？主要集中在哪些问题？是否与生产质量波动有关？",
            "系统A长续航版（EV002）上市后，老款（EV001）的销量是否被明显分流？分流程度如何？",
            "客户反馈中‘OTA升级失败’是否集中在P202402及之后的生产批次？是否与软件版本有关？",
            "系统A在三四线城市的销量是否低于预期？是否与营销活动覆盖不足有关？",
          ],
        },
        {
          subCategory: "销售预测",
          questions: [
            "系统B的销量增长是否与其营销预算增加显著相关？ ROI 如何？",
            "系统A的客户净推荐值（NPS）是否持续低于行业平均水平？主要原因是什么？",
            "若维持当前趋势，系统A在2024年Q4的销量预计为多少？关键影响因素有哪些？",
          ],
        },
      ],
    },
    {
      type: "kb",
      category: "知识库",
      subCategories: [
        {
          subCategory: "文本引用",
          questions: [
            "碳酸锂价格上涨对成本影响",
            "2024年4月华南暴雨对物流影响",
          ],
        },
        {
          subCategory: "视频应用",
          questions: [
            "1.5T 发动机与 1.8L 自然吸气发动机在动力上有何关联？根据知识库内容，排量后面带 “T” 代表什么含义，其相比同排量自然吸气发动机动力提升幅度是多少？",
            "汽车的轴距和轮距分别影响车辆的哪些方面？为什么说车身长短不能决定车内空间大小？",
            "在雾天行车时，正确的车灯使用方式是什么？为什么不能只开双闪或远光灯？另外，查看二手理想 L9 轮胎时，除了轮胎宽度、扁平比、适配轮骨尺寸，还需关注哪项关键信息，该信息如何解读？",
          ],
        },
        {
          subCategory: "图片引用",
          questions: ["动态推测解码 Transformers库提供了哪些方法?"],
        },
        {
          subCategory: "PDF引用",
          questions: ["java编程规范有哪些"],
        },
      ],
    },
    {
      type: "web",
      category: "联网搜索",
      subCategories: [
        {
          subCategory: "实时资讯",
          questions: ["给我整理一下最新的AI资讯", "给我最新的汽车行业资讯"],
        },
      ],
    },
    {
      type: "dataset",
      category: "智能问数",
      subCategories: [
        {
          subCategory: "生产与质量分析",
          questions: [
            "查看产品系统B旗舰版（EV101）的所有生产批次及其缺陷率",
            "生产缺陷率高于1.5%的批次，对应的销量是否下降？（以EV001为例）",
            "生产批次缺陷率与客户反馈负面情绪的相关性分析（EV001）",
            "哪些产品既存在生产延迟，又有大量负面反馈？",
          ],
        },
        {
          subCategory: "客户反馈与满意度",
          questions: [
            "找出所有关于“中控死机”的客户反馈内容",
            "列出所有负面情绪的客户反馈（按时间排序）",
            "客户反馈中提到“补贴”的产品，是否真的不符合补贴政策？",
            "哪些区域的客户反馈负面最多？",
            "客户反馈中“OTA失败”集中在哪些产品？",
            "产品价格与客户反馈情绪的关系：高价产品是否更满意？",
          ],
        },
        {
          subCategory: "营销活动与效果",
          questions: [
            "列出2024年上半年所有已完成的营销活动",
            "查询营销预算超过100万元的活动名称和实际支出",
            "营销活动取消的产品，其销量是否受影响？（如EV001春季活动）",
            "EV101在2024年每月的销量与营销投入对比",
            "续航低于400km的产品中，哪些有营销活动但销量仍不佳？",
            "列出所有正在进行的营销活动及其关联产品的续航和价格",
            "营销活动期间销量是否有明显提升？以EV101五一活动为例",
            "哪些产品的营销预算远高于实际支出？可能存在执行问题",
          ],
        },
        {
          subCategory: "销售与市场表现",
          questions: [
            "查询系统A在2024年1月的销量数据，包括区域和销量",
            "查看华南地区2024年每月的销量目标总和",
            "系统A在2024年Q1的实际销量与目标销量对比",
            "查询续航低于400km的产品有哪些？",
            "华南地区因暴雨影响交付的月份，销量是否显著下降？",
            "系统A长续航版（EV002）上市后，老款（EV001）销量变化如何？",
            "生产数量少但销量高的产品，是否存在库存风险？",
          ],
        },
      ],
    },
  ],
};

interface CategoryPanelProps {
  onClick: (text: string) => void;
  onDoubleClick: (text: string) => void;
}

const CategoryPanel: React.FC<CategoryPanelProps> = ({
  onClick,
  onDoubleClick,
}) => {
  const [expanded, setExpanded] = useState<string | null>(null);
  const { chatType, setChatType } = useConversionStore((state) => ({
    setChatType: state.setChatType,
    chatType: state.chatType,
  }));

  const { t } = useTranslation();

  const toggleCategory = (cat: any) => {
    setExpanded((prev) => (prev === cat.category ? null : cat.category));
    if (cat.type !== chatType) {
      setChatType(cat.type);
    }
  };

  return (
    <div className="w-full p-4 bg-white rounded-md shadow-md">
      {data.categories.map((cat, idx) => {
        const icon = icons[idx % icons.length];
        const color = iconColors[idx % iconColors.length];
        const isOpen = expanded === cat.category;

        return (
          <div key={cat.category} className="mb-4 border-b pb-2">
            <div
              className="flex items-center cursor-pointer text-base font-semibold hover:text-blue-600"
              onClick={() => toggleCategory(cat)}
            >
              <span className="text-xl mr-2" style={{ color }}>
                {icon}
              </span>
              <span className="flex-1">{t(`common.chatType.${cat.type}`)}</span>
              {isOpen ? <AiOutlineDown /> : <AiOutlineRight />}
            </div>

            {isOpen && (
              <div className="mt-2 pl-6 space-y-3 text-sm text-gray-800">
                {cat.subCategories.map((sub) => (
                  <div key={sub.subCategory}>
                    <div className="font-medium text-gray-900 mb-1">
                      {sub.subCategory}
                    </div>
                    <ul className="list-disc list-inside space-y-1">
                      {sub.questions.map((q) => (
                        <li
                          key={q}
                          onClick={() => onClick(q)}
                          onDoubleClick={() => onDoubleClick(q)}
                          className="hover:text-blue-500 cursor-pointer"
                        >
                          {q}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default CategoryPanel;

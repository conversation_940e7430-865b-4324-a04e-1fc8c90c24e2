import React from 'react';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '../ui/button';
import { RiQuillPenAiLine } from "react-icons/ri";
import { AiOutlineDelete } from "react-icons/ai";
import { Conversion, useConversionStore } from '@/store/useConversion';
import { shallow } from 'zustand/shallow';
import { cn } from '@/lib/utils';
import { useState, useRef, useEffect } from 'react';
import useIsMobile from '@/hooks/useIsMobile';
import { useTranslation } from 'react-i18next';
interface SiderbarItemProps {
  item: Conversion
  index: number
}


const SiderbarItem = ({ item, index }: SiderbarItemProps) => {
  const isMobile = useIsMobile();
  const [showMenu, setShowMenu] = useState(false)
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState<string>('');
  const inputRef = useRef<HTMLInputElement>(null);
  const menuRef = useRef<HTMLDivElement>(null)

  const { curConversionId, changeConversion, updateConversionName, removeConversion } = useConversionStore((state) => ({
    curConversionId: state.curConversionId,
    changeConversion: state.changeConversion,
    updateConversionName: state.updateConversionName,
    removeConversion: state.removeConversion,
  }), shallow)

  const { t } = useTranslation()

  const switchBar = async () => {
    if (curConversionId === item.id) return;
    setIsEditing(false)
    await changeConversion(item.id)
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    setEditValue(item.title!) // 设置编辑时的初始名称
    setIsEditing(true)
    setShowMenu(false)
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    removeConversion(item.id)
    // removeConversion(index)
    setShowMenu(false)
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node))
        setShowMenu(false)
    }
    if (showMenu)
      document.addEventListener('mousedown', handleClickOutside)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showMenu])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node))
        setIsEditing(false)
      if (!editValue.trim()) return
      updateConversionName(item.id, editValue);
    }
    if (isEditing)
      document.addEventListener('mousedown', handleClickOutside)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isEditing, editValue, item.id, updateConversionName])

  return (
    <div
      onClick={() => switchBar()}
      className={cn('relative w-full h-10 text-[.875rem] flex items-center rounded-xl cursor-pointer hover:bg-[#F0F6FE] group', {
        'bg-[#DEE9FC]': curConversionId === item.id,
        'overflow-visible': showMenu,
      })}
    >

      {(isMobile || curConversionId === item.id) && isEditing ? (
        <input
          ref={inputRef}
          type="text"
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          maxLength={40}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              setIsEditing(false)
              updateConversionName(item.id, editValue)
            }
          }}
          onClick={e => e.stopPropagation()} // 阻止事件冒泡
          autoFocus
          className='w-full border outline-none border-blue-400 rounded-md px-2 py-2 text-sm font-medium'
        />
      ) : (
        <div className="truncate flex-shrink p-2 flex-1 min-w-0">{item.title || t('common.sidebar.add_conversation')}</div>
      )}
      {(isMobile || curConversionId === item.id) && !isEditing && item.id !== '-1' && (
        <div className="group-hover/item:block">
          <div className="group/dots relative">
            <div
              className="cursor-pointer text-gray-950"
              onClick={(e) => {
                e.stopPropagation()
                setShowMenu(!showMenu)
              }}
            >
              <Button variant="ghost" className="h-8 w-8 p-0 cursor-pointer">
                <MoreHorizontal />
              </Button>
            </div>

            {showMenu && (
              <div
                ref={menuRef}
                className="flex flex-col gap-y-2 absolute w-24  right-2 top-8 bg-white shadow-lg rounded-md p-2 z-10"
              >
                <div
                  onClick={handleEdit}
                  className="menu-item text-blue-600 flex gap-x-2 items-center justify-center hover:bg-[#F5F5F5] p-2 rounded-md"
                >
                  <RiQuillPenAiLine />
                  重命名
                </div>
                <div
                  onClick={handleDelete}
                  className="menu-item text-red-600 flex gap-x-2 items-center justify-center hover:bg-[#F5F5F5] p-2 rounded-md"
                >
                  <AiOutlineDelete className='text-red-500' />
                  <span className='text-red-500'>删除</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default SiderbarItem;

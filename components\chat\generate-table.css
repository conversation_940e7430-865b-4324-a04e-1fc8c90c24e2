.custom-table .ant-table-tbody>tr>td {
  padding: 4px !important;
  /* 调整单元格内边距 */
  line-height: 1.2 !important;
  /* 调整行高 */
}

.custom-table .ant-table-tbody>tr.ant-table-measure-row>td {
  padding: 0px !important;
}

/* 如果需要调整表头高度 */
.custom-table .ant-table-thead>tr>th {
  padding: 4px !important;
}

/* 美化 Tooltip 中的滚动条 */
.ant-tooltip-inner {
  /* 滚动条整体部分 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
    cursor: pointer;
  }

  /* 滚动条滑块悬浮效果 */
  ::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
}
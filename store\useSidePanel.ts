import { shallow } from "zustand/shallow";
import { createWithEqualityFn } from "zustand/traditional";

type PanelType = 'web-search' | 'reference' | 'report' | null;

type SidePanelStore = {
  isOpen: boolean;
  panelType: PanelType;
  openSidePanel: (type: PanelType) => void;
  closeSidePanel: () => void;
}

export const useSidePanelStore = createWithEqualityFn<SidePanelStore>((set) => ({
  isOpen: false,
  panelType: null,
  openSidePanel: (type) => set({ isOpen: true, panelType: type }),
  closeSidePanel: () => set({ isOpen: false }),
}), shallow);
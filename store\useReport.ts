import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";
import { fetchDeepResearchReport } from "@/services/api";

export interface ChapterItem {
  url: string;
  title: string;
  summary: string;
}

export interface ReportData {
  // 关联标识
  msgId?: string; // 前端当前选中消息ID
  conversation_id?: string;
  message_id?: string;

  // 报告封面信息（新返回结构）
  title: string;
  summary?: string;
  chapters?: ChapterItem[];

  // 兼容字段（旧实现可能只返回目录链接或HTML）
  tocUrl?: string; // 报告目录页链接
  htmlContent?: string; // 直接HTML内容
}

interface ReportStore {
  currentReport: ReportData | null;
  // 正在更新（流式生成）中的消息ID
  updatingMessageId: string | null;
  // 存放各条消息的最终报告，避免不同消息之间相互覆盖
  reportsByMessageId: Record<string, ReportData>;
  // 报告流式加载状态
  isReportLoading: boolean;
  setCurrentReport: (report: ReportData) => void;
  clearCurrentReport: () => void;
  // 生成报告（普通接口），完成后将 tocUrl 写入缓存
  generateReport: (messageId: string, conversationId: string) => Promise<void>;
  // 兼容旧调用名
  fetchFinalReport: (messageId: string, conversationId: string) => Promise<void>;
  // 存储与获取最终报告（供面板查询）
  setReportForMessage: (messageId: string, report: ReportData) => void;
  getReportByMessageId: (messageId: string) => ReportData | null;
}

export const useReportStore = create(
  subscribeWithSelector<ReportStore>((set, get) => ({
    currentReport: null,
    updatingMessageId: null,
    reportsByMessageId: {},
    isReportLoading: false,
    setCurrentReport: (report) => {
      set({ currentReport: report });
    },
    clearCurrentReport: () => set({ currentReport: null }),
    generateReport: async (messageId: string, conversationId: string) => {
      // 开始加载状态
      set(() => ({
        updatingMessageId: messageId,
        isReportLoading: true,
        currentReport: {
          msgId: messageId,
          title: "分析报告",
        },
      }));

      try {
        const res: any = await fetchDeepResearchReport({ message_id: messageId, conversation_id: conversationId });
        const data = res?.data || {};

        // 新返回结构适配
        const report: ReportData = {
          msgId: messageId,
          conversation_id: data.conversation_id ?? conversationId,
          message_id: data.message_id ?? messageId,
          title: data.title || "分析报告",
          summary: data.summary,
          chapters: Array.isArray(data.chapters) ? data.chapters : undefined,
          // 兼容旧字段（如果后端同时返回 toc_url 也一并保存）
          tocUrl: data.toc_url,
        };

        set((state) => ({
          reportsByMessageId: {
            ...state.reportsByMessageId,
            [messageId]: report,
          },
          currentReport: report,
          updatingMessageId: null,
          isReportLoading: false,
        }));
      } catch (e) {
        console.error("generateReport failed:", e);
        set({ isReportLoading: false, updatingMessageId: null });
      }
    },
    fetchFinalReport: async (messageId: string, conversationId: string) => {
      // 兼容旧调用名，直接转发到 generateReport
      await get().generateReport(messageId, conversationId);
    },
    setReportForMessage: (messageId, report) =>
      set((state) => ({
        reportsByMessageId: {
          ...state.reportsByMessageId,
          [messageId]: report,
        },
      })),
    getReportByMessageId: (messageId) => {
      const { reportsByMessageId } = get();
      return reportsByMessageId[messageId] || null;
    },
  }))
);

import React, { memo } from 'react';
import Markdown from '../custom/markdown';

interface QuestionProps {
  item: any
}

const Question = ({ item }: QuestionProps) => {
  return (
    <div className='flex space-y-2 flex-row-reverse'>
      <div className='lg:w-10 w-0 h-10 rounded-full overflow-hidden ml-2 flex-shrink-0'>
        <div className='w-full h-full object-cover' />
      </div>
      <div className='w-fit  p-3 rounded-lg md-font-size bg-[#F0F6FE] text-[#262626] lg:max-w-[95%] max-w-[99%]'>
        <Markdown content={item.content} />
      </div>
    </div>
  );
}

export default memo(Question);

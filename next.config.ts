import type { NextConfig } from "next";
const { codeInspectorPlugin } = require('code-inspector-plugin')

const nextConfig: NextConfig = {
  /* config options here */
  webpack: (config, { dev, isServer }) => {
    config.plugins.push(codeInspectorPlugin({ bundler: 'webpack' }))
    return config
  },
  productionBrowserSourceMaps: false, // enable browser source map generation during the production build
  pageExtensions: ['ts', 'tsx', 'js', 'jsx', 'md', 'mdx'],
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // https://nextjs.org/docs/api-reference/next.config.js/ignoring-typescript-errors
    ignoreBuildErrors: true,
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: '/chat',
        permanent: false, 
      },
    ]
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://************:8002/api/:path*', // 开发环境代理地址
        basePath: false
      },
    ];
  },
  reactStrictMode: false,
};

export default nextConfig;

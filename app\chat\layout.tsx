'use client'

import AppSkeleton from '@/components/custom/app-skeleton';
import MobileMenu from '@/components/custom/mobile-menu';
import Siderbar from '@/components/custom/siderbar';
import { apiAuth } from '@/lib/feishu';
import { dingding } from '@/lib/dingding';
import { useConversionStore } from '@/store/useConversion';
import React, { Fragment, useEffect, useLayoutEffect, useState } from 'react';
import { shallow } from 'zustand/shallow';
import { useTranslation } from 'react-i18next'
import { usePlatform } from '@/lib/usePlatform';
import { useRouter } from 'next/navigation';

const ChatLayout = ({ children }: { children: React.ReactNode }) => {
  const [authPassed, setAuthPassed] = useState(false);
  const [accessToken, setAccessToken] = useState('')
  const router = useRouter()
  const { t } = useTranslation()
  const { initConversion, initLoading, systemConfig } = useConversionStore((state) => ({
    initConversion: state.initConversion,
    initLoading: state.initLoading,
    systemConfig: state.systemConfig
  }), shallow)
  const { isDingTalk, isFeishu } = usePlatform()

  const feishuAuth = async () => {
    const isAuthed = await apiAuth();
    setAuthPassed(isAuthed);
  }

  const dingdingAuth = async () => {
    // 获取url的query参数
    const isAuthed = await dingding();
    setAuthPassed(isAuthed);
  }

  const authPlatformLogin = async () => {
    if (isFeishu) {
      feishuAuth()
    } else if (isDingTalk) {
      dingdingAuth()
    } else {
      if (!accessToken) {
        router.push('/login')
      } else {
        setAuthPassed(true)
      }
    }
  }

  useEffect(() => {
    const token = localStorage.getItem('access_token');
    if (token) {
      setAccessToken(token)
      setAuthPassed(true)
    } else {
      authPlatformLogin()
    }
  }, [])

  // useEffect(() => {
  //   authPlatformLogin()
  // }, [])

  useEffect(() => {
    if (authPassed) {
      initConversion();
    }
  }, [authPassed])

  useEffect(() => {
    if (systemConfig) {
      document.title = t('common.sidebar.title');
    }
  }, [systemConfig])

  if (!authPassed || initLoading) {
    return <AppSkeleton />
  }

  return (
    <div className='h-svh w-screen flex relative'>
      <div className='hidden lg:block'>
        <Siderbar />
      </div>
      <div className='lg:hidden block'>
        <MobileMenu />
      </div>
      <Fragment>
        {children}
      </Fragment>
    </div>
  );
}

export default ChatLayout;

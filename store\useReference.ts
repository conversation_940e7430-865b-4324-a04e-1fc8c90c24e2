import { shallow } from "zustand/shallow";
import { createWithEqualityFn } from "zustand/traditional";


type ReferenceItem = {
  type: string;
  source: string
  fragment?: string
  page?: number;
  [key: string]: any;
}

type ReferenceStore = {
  referenceMap: Map<string, ReferenceItem>;
  addReference: (item: ReferenceItem) => void;
  getReference: (source: string) => ReferenceItem | undefined;
  clearReference: () => void;
  currentReference: ReferenceItem | undefined;
}

export const useReferenceStore = createWithEqualityFn<ReferenceStore>((set, get) => ({
  currentReference: undefined,
  referenceMap: new Map(),
  addReference: (item) => {
    const map = new Map(get().referenceMap);
    if (!map.has(item.source)) {
      map.set(item.source, item);
      set({ referenceMap: map });
    }
    set({ currentReference: item });
  },
  getReference: (source) => {
    return get().referenceMap.get(source);
  },
  clearReference: () => set({ referenceMap: new Map() }),
}), shallow);

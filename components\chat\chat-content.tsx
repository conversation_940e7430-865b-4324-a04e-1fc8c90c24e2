import React, { useEffect } from 'react';
import Answer from './answer';
import Question from './question';
import { useScrollToBottom, useSticky } from 'react-scroll-to-bottom';

interface ChatContentProps {
  list: any[];
  onScrollToBottomReady?: (scrollFn: () => void) => void;
  onStickyChange?: (isSticky: boolean) => void;
}

const ChatContent = ({ list, onScrollToBottomReady, onStickyChange }: ChatContentProps) => {

  const scrollToBottom = useScrollToBottom();
  const [sticky] = useSticky()

  useEffect(() => {
    if (onScrollToBottomReady && scrollToBottom) {
      onScrollToBottomReady(scrollToBottom);
    }
  }, [scrollToBottom, onScrollToBottomReady]);

  useEffect(() => {
    if (onStickyChange) {
      onStickyChange(sticky);
    }
  }, [sticky, onStickyChange]);

  return (
    <div className='pb-[5rem] flex-1'>

      <div className='lg:w-[900px] max-w-full overflow-hidden  w-full mx-auto space-y-6 p-4'>
        {
          list?.map((item, index) => {
            if (item.role === 'assistant') {
              const isLast = item.id === list[list.length - 1].id
              return (
                <div key={`assistant-message-${index}-${item.id}`} className="message-item" data-id={`assistant-${item.id}`}>
                  <div className='max-w-[100%]'>
                    <Answer item={item} isLast={isLast} />
                  </div>
                </div>
              )
            }
            return (
              <div key={`user-message-${index}-${item.id}`} className="message-item flex justify-end" data-id={`user-${item.id}`}>
                <div className='max-w-[100%]'>
                  <Question item={item} />
                </div>
              </div>
            )
          })
        }
      </div>
    </div>
  );
}

export default ChatContent;

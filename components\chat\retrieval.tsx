import React, { useEffect, useState } from 'react';
import { AiOutlineExpand } from "react-icons/ai";
import { Modal } from 'antd'
import { VscTable } from "react-icons/vsc";
import { FiPieChart } from "react-icons/fi";
import { AiOutlineConsoleSql } from "react-icons/ai";
import { hasValue } from '@/lib/utils';
import useIsMobile from '@/hooks/useIsMobile';
import TableView from './table-view';
import ChartView from './chart-view';
import SQLView from './sql-view';
import { useTranslation } from 'react-i18next';

const Retrieval = ({ sql, echart, table }: { sql: string, echart: any, table: any[] }) => {
  const [selectType, setSelectType] = useState('table')
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newChatOption, setNewChatOption] = useState({})
  const { t } = useTranslation()
  const isMobile = useIsMobile();

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const options = [
    { type: 'table', name: `${t('common.show.table')}`, icon: <VscTable /> },
    { type: 'echart', name: `${t('common.show.chart')}`, icon: <FiPieChart /> },
    { type: 'sql', name: `${t('common.show.sql')}`, icon: <AiOutlineConsoleSql /> },
  ]

  useEffect(() => {
    if (echart) {
      const isPieChart = echart.series?.[0]?.type === 'pie';
      const option = {
        ...echart,
        tooltip: {
          ...echart.tooltip,
          confine: false, // 让提示框不被限制在图表区域内
          enterable: true, // 鼠标可进入提示框内
          appendToBody: true, // 将提示框添加到 body 上，防止被容器裁切
          extraCssText: 'max-width: none; white-space: pre-wrap; z-index: 1000;', // 允许内容换行且取消宽度限制
          textStyle: {
            overflow: 'break', // 文字换行
            width: 'auto',
          },
        },
        ...(isPieChart ? {
          legend: {
            orient: "horizontal",
            bottom: "0%",
            left: "center",
            type: "scroll",
            padding: [10, 0, 10, 0]
          }
        } : {})
      }
      setNewChatOption(option)
    }
  }, [echart])

  return (
    <div className="overflow-x-auto w-full my-4 rounded-lg py-2 px-4 bg-[#F3F4F6]">
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-2'>
          {
            options
              .filter((item) => item.type !== 'echart' || (echart && hasValue(echart)))
              .map((item, index) => (
                <div
                  key={index}
                  onClick={() => setSelectType(item.type)}
                  className={selectType === item.type ? 'bg-white text-[#2F5FDD] px-4 py-1 cursor-pointer' : 'rounded-lg px-4 py-1 cursor-pointer'}
                >
                  <div className='flex items-center gap-x-1'>
                    <div>{item.icon}</div>
                    <div className='md-font-size'>{item.name}</div>
                  </div>
                </div>
              ))
          }
        </div>
        <div className='cursor-pointer' onClick={() => showModal()}>
          <AiOutlineExpand className='text-xl' />
        </div>
      </div>
      <div>
        {
          selectType === 'table' ? (
            <TableView dataSource={table} />
          ) :
            selectType === 'echart' ?
              (
                <div style={{
                  marginTop: '20px',
                  minHeight: '420px',
                  minWidth: '100%',
                  overflowX: 'auto', // 改为auto自动显示滚动条
                  position: 'relative' // 添加定位上下文
                }}>
                  {
                    (echart && hasValue(echart)) ? (
                      <ChartView option={newChatOption} />
                    ) : (
                      <div style={{ textAlign: 'center', marginTop: '100px' }}>暂无数据</div>
                    )
                  }
                </div>
              )
              : selectType === 'sql' ?
                <SQLView sql={sql} /> :
                null
        }
      </div>

      <Modal
        title={t('common.fullScreen')}
        open={isModalOpen}
        onCancel={handleCancel}
        footer={null}
        width={isMobile ? '100%' : '60%'}
      >
        <div style={{ marginTop: '20px', height: 'auto', overflow: 'auto' }}>
          {selectType === 'table' ? (
            <TableView dataSource={table} />
          ) : selectType === 'echart' ?
            <ChartView option={newChatOption} /> :
            selectType === 'sql' ? <SQLView sql={sql} /> : null}
        </div>
      </Modal>
    </div>
  );
}

export default Retrieval;

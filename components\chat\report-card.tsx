import React from 'react';
import { useSidePanelStore } from '@/store/useSidePanel';
import { useReportStore } from '@/store/useReport';
import { HiOutlineDocumentReport } from 'react-icons/hi';
import { LuExternalLink } from 'react-icons/lu';

interface ReportCardProps {
  title: string;
  htmlContent?: string;
}

export const ReportCard: React.FC<ReportCardProps> = ({ title, htmlContent }) => {
  const { openSidePanel } = useSidePanelStore();
  const { setCurrentReport } = useReportStore();

  const handleOpenReport = () => {
    // 将报告内容存储到全局状态
    setCurrentReport({ title, htmlContent });
    // 打开侧边栏
    openSidePanel('report');
  };

  return (
    <div className="mt-4 border border-gray-200 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow">
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={handleOpenReport}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <HiOutlineDocumentReport className="w-5 h-5 text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                {title}
              </h3>
              <p className="text-xs text-gray-500 mt-1">
                点击查看详细报告
              </p>
            </div>
          </div>
          <div className="flex-shrink-0">
            <LuExternalLink className="w-4 h-4 text-gray-400" />
          </div>
        </div>
      </div>
    </div>
  );
};

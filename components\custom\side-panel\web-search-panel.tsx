// components/custom/side-panel/web-search-panel.tsx
import React from 'react';
import { useWebSearchStore } from '@/store/useWebSearch';
import { shallow } from 'zustand/shallow';

export const WebSearchPanel = () => {
  const { searchResults } = useWebSearchStore(
    (state) => ({
      searchResults: state.searchResults,
    }),
    shallow
  );

  if (!searchResults || searchResults.length === 0) {
    return <div className="p-4 text-gray-400">暂无搜索结果</div>;
  }

  return (
    <div className="p-4">
      <div className="space-y-6">
        {searchResults.map((result, index) => (
          <div key={index} className="border-b pb-4 last:border-b-0 hover:bg-gray-50 p-3 rounded-lg">
            {/* 标题区域 */}
            <h3 className="text-lg font-medium text-blue-600 mb-1 line-clamp-2">
              <a href={result.url} target="_blank" rel="noopener noreferrer" className="hover:underline flex items-start">
                {result.favicon && (
                  <span className="mr-2 inline-block w-5 h-5 flex-shrink-0">
                    <img
                      src={result.favicon}
                      alt=""
                      className="w-full h-full object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                  </span>
                )}
                <span className="ml-2 text-sm">{result.title || '无标题'}</span>
              </a>
            </h3>

            {/* URL 显示 */}
            <p className="text-sm text-gray-500 mb-2 flex items-center">
              <span className="truncate max-w-[90%]">{result.url}</span>
              <span className="ml-1 text-gray-400 text-xs border border-gray-300 rounded px-1">
                {index + 1}
              </span>
            </p>

            {/* 内容摘要 */}
            <p className="text-sm text-gray-700 line-clamp-3">
              {result.context}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};
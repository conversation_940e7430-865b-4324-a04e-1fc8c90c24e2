'use client'

import ChatContent from '@/components/chat/chat-content';
import TextareaInput from '@/components/chat/textarea-input';
import Welcome from '@/components/chat/welcome';
import Header from '@/components/custom/header';
import Loading from '@/components/custom/loading/loading';
import MessageLoading from '@/components/custom/loading/message-loading';
import { useConversionStore } from '@/store/useConversion';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { shallow } from 'zustand/shallow';
import ScrollToBottom, { useObserveScrollPosition, useAtStart } from 'react-scroll-to-bottom';
import { SidePanel } from '@/components/custom/side-panel';
import { useSidePanelStore } from '@/store/useSidePanel';
import styles from './page.module.css';
import classNames from 'classnames';
import useIsMobile from '@/hooks/useIsMobile';

// 创建一个包装组件来使用useObserveScrollPosition和useAtStart钩子
const ScrollContainer = ({ children, onScroll, onAtStart }: {
  children: React.ReactNode,
  onScroll: (e: any) => void,
  onAtStart: (isAtStart: boolean) => void
}) => {
  useObserveScrollPosition((scrollPosition) => {
    // 每次滚动位置变化时都调用onScroll
    onScroll(scrollPosition);
  });

  // 使用useAtStart钩子检测是否滚动到了顶部
  const [atStart] = useAtStart();

  // 当滚动到顶部状态变化时调用onAtStart回调
  useEffect(() => {
    onAtStart(atStart);
  }, [atStart, onAtStart]);

  return <>{children}</>;
};

const Chat = () => {
  const { isChatStarted, conversions, curConversionId, messageLoading, isResponding } = useConversionStore((state) => ({
    isChatStarted: state.isChatStarted,
    messageLoading: state.messageLoading,
    conversions: state.conversions,
    curConversionId: state.curConversionId,
    isResponding: state.isResponding
  }), shallow);

  const { isOpen: isSidePanelOpen, panelType, closeSidePanel } = useSidePanelStore((state) => ({
    isOpen: state.isOpen,
    panelType: state.panelType,
    closeSidePanel: state.closeSidePanel
  }), shallow);

  const isMobile = useIsMobile(768);

  const [scrollToBottom, setScrollToBottom] = useState<(() => void) | null>(null);
  const [isSticky, setIsSticky] = useState(true);

  // 分页控制
  const [displayedMessages, setDisplayedMessages] = useState<any[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [pageSize] = useState(10); // 每页显示的消息数量
  const [hasLoadedAll, setHasLoadedAll] = useState(false);

  const handleScrollToBottomReady = useCallback((scrollFn: () => void) => {
    setScrollToBottom(() => scrollFn);
  }, []);

  const handleStickyChange = useCallback((sticky: boolean) => {
    setIsSticky(sticky);
  }, []);

  const curConversion = useMemo(() => {
    return conversions.find((item) => item.id === curConversionId);
  }, [conversions, curConversionId]);

  // 加载更多消息
  const loadMoreMessages = useCallback(() => {
    if (!curConversion?.messages || isLoadingMore || hasLoadedAll) return;

    setIsLoadingMore(true);

    // 简化滚动定位逻辑，使用更可靠的方法
    setTimeout(() => {
      const allMessages = curConversion.messages;
      const currentCount = displayedMessages.length;
      const newStartIndex = Math.max(0, allMessages.length - currentCount - pageSize);
      const newEndIndex = allMessages.length - currentCount;

      // 如果已经加载到第一条消息，标记为已全部加载
      if (newStartIndex === 0) {
        setHasLoadedAll(true);
      }

      // 将新加载的消息添加到显示消息列表的前面
      const newMessages = allMessages.slice(newStartIndex, newEndIndex);

      // 记住第一条消息的ID和角色，用于后续定位
      const firstVisibleMessage = displayedMessages.length > 0 ? displayedMessages[0] : null;

      setDisplayedMessages(prev => [
        ...newMessages,
        ...prev
      ]);

      // 添加一个短暂延时，确保DOM已更新
      setTimeout(() => {
        try {
          // 如果有第一条可见消息，尝试滚动到该消息
          if (firstVisibleMessage) {
            // 根据消息角色构建正确的data-id
            const dataId = firstVisibleMessage.role === 'assistant'
              ? `assistant-${firstVisibleMessage.id}`
              : `user-${firstVisibleMessage.id}`;

            // 查找带有正确data-id的元素
            const targetElement = document.querySelector(`[data-id="${dataId}"]`);

            if (targetElement) {
              // 使用更安全的scrollIntoView方法
              targetElement.scrollIntoView({ behavior: 'auto', block: 'start' });
            } else {
              console.log('未找到目标元素:', dataId);
            }
          }
        } catch (error) {
          console.error('滚动定位出错:', error);
        }

        setIsLoadingMore(false);
      }, 100); // 增加延时，确保DOM完全更新
    }, 300);
  }, [curConversion?.messages, displayedMessages, hasLoadedAll, isLoadingMore, pageSize]);

  // 处理滚动位置，移到这里确保hooks顺序一致
  const handleScrollPosition = useCallback(({ scrollTop }: { scrollTop: number }) => {
    // 只在滚动到顶部附近时（小于100px）才加载更多消息
    if (scrollTop < 100 && !hasLoadedAll && !isLoadingMore) {
      loadMoreMessages();
    }
  }, [hasLoadedAll, isLoadingMore, loadMoreMessages]);

  // 处理滚动到顶部的事件
  const handleAtStart = useCallback((isAtStart: boolean) => {
    if (isAtStart && !hasLoadedAll && !isLoadingMore) {
      loadMoreMessages();
    }
  }, [hasLoadedAll, isLoadingMore, loadMoreMessages]);

  // 初始化和更新消息
  useEffect(() => {
    if (curConversion?.messages) {
      const allMessages = curConversion.messages;
      // 如果消息总数小于页面大小，则直接设置为显示所有消息
      if (allMessages.length <= pageSize || allMessages.length === 0) {
        setDisplayedMessages(allMessages);
        setHasLoadedAll(true);
      } else {
        // 否则显示最后一页的消息
        const startIndex = Math.max(0, allMessages.length - pageSize);
        setDisplayedMessages(allMessages.slice(startIndex));
        setHasLoadedAll(false);
      }
    }
  }, [curConversion?.messages, pageSize]);

  if (messageLoading) {
    return (
      <div className='w-full h-full flex items-center justify-center'>
        <Loading />
      </div>
    );
  }

  return (
    <>
      {!isChatStarted ?
        <Welcome /> :
        <div
          className={classNames(styles.container, { [styles.sidebarVisible]: isSidePanelOpen && !isMobile })}
          data-panel-type={panelType}
        >
          <div className={styles.mainContent}>
            <ScrollToBottom
              initialScrollBehavior="auto"
              followButtonClassName='hidden'
              className='flex-1 overflow-hidden no-scrollbar'
            >
              <ScrollContainer onScroll={handleScrollPosition} onAtStart={handleAtStart}>
                <>
                  <Header />
                  <div className='flex-grow'>
                    {!hasLoadedAll && (
                      <div className='text-center py-3 text-gray-500'>
                        {isLoadingMore ? (
                          <div className="flex items-center justify-center">
                            <MessageLoading />
                          </div>
                        ) : (
                          <button
                            onClick={loadMoreMessages}
                            className='px-4 py-2 bg-gray-100 rounded hover:bg-gray-200'
                          >
                            加载更多消息
                          </button>
                        )}
                      </div>
                    )}
                    <ChatContent
                      list={displayedMessages}
                      onStickyChange={handleStickyChange}
                      onScrollToBottomReady={handleScrollToBottomReady}
                    />
                  </div>
                </>
              </ScrollContainer>
            </ScrollToBottom>
            <TextareaInput isBottom={isSticky} scrollToBottom={scrollToBottom || (() => { })} />
          </div>

          {/* PC端侧边栏 */}
          {!isMobile && (
            <div className={styles.sidebar}>
              {isSidePanelOpen && <SidePanel />}
            </div>
          )}

          {/* 移动端侧边栏 - 绝对定位覆盖 */}
          {isMobile && isSidePanelOpen && (
            <div className="absolute inset-0 z-50 flex flex-col">
              {/* 遮罩层 */}
              <div className="absolute inset-0 bg-black/50" onClick={closeSidePanel} />

              {/* 侧边栏内容 - 从底部滑入 */}
              <div className="absolute inset-x-0 bottom-0 bg-white rounded-t-2xl shadow-2xl transform transition-transform duration-300 ease-out"
                style={{ height: 'calc(100% - 80px)' }}>
                <SidePanel />
              </div>
            </div>
          )}
        </div>
      }
    </>
  );
};

export default Chat;
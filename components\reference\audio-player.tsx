import React, { useRef, useState, useEffect } from "react";
import {
  LuPlay,
  LuPause,
  LuDownload,
  LuVolume2,
  LuVolumeX,
} from "react-icons/lu";

interface AudioPlayerProps {
  reference: {
    source: string;
    fragment?: string;
    start_ms?: number;
  };
  customStyles?: {
    primaryColor?: string;
    backgroundColor?: string;
  };
}

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  reference,
  customStyles = {},
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(1);

  const { start_ms = 0 } = reference;
  const startTime = start_ms / 1000;

  const {
    primaryColor = "#3b82f6",
    backgroundColor = "#f8fafc",
  } = customStyles;

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      if (startTime < audio.duration) {
        audio.currentTime = startTime;
        setCurrentTime(startTime);
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("timeupdate", handleTimeUpdate);
    audio.addEventListener("play", handlePlay);
    audio.addEventListener("pause", handlePause);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("timeupdate", handleTimeUpdate);
      audio.removeEventListener("play", handlePlay);
      audio.removeEventListener("pause", handlePause);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [startTime]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;
    isPlaying ? audio.pause() : audio.play();
  };

  const toggleMute = () => {
    const audio = audioRef.current;
    if (!audio) return;
    if (isMuted) {
      audio.volume = volume;
      setIsMuted(false);
    } else {
      audio.volume = 0;
      setIsMuted(true);
    }
  };

  const downloadAudio = () => {
    const link = document.createElement("a");
    link.href = reference.source;
    link.download = "audio.mp3";
    link.click();
  };

  const formatTime = (time: number) => {
    const min = Math.floor(time / 60);
    const sec = Math.floor(time % 60).toString().padStart(2, "0");
    return `${min}:${sec}`;
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const audio = audioRef.current;
    const progressBar = progressRef.current;
    if (!audio || !progressBar) return;

    const rect = progressBar.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;

    const newTime = percentage * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const progressPercent = (currentTime / duration) * 100 || 0;

  return (
    <div className="w-full p-4 bg-white rounded-lg shadow-md flex flex-col space-y-3">
      <audio ref={audioRef} src={reference.source} preload="metadata" />

      {/* 控制栏 */}
      <div className="flex items-center space-x-4">
        <button
          onClick={togglePlay}
          className="p-2 bg-gray-100 rounded hover:bg-gray-200 transition"
          style={{ color: primaryColor }}
        >
          {isPlaying ? <LuPause size={20} /> : <LuPlay size={20} />}
        </button>

        <div className="text-sm text-gray-600">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>

        <button
          onClick={toggleMute}
          className="p-2 text-gray-700 hover:text-blue-600 transition"
        >
          {isMuted ? <LuVolumeX size={18} /> : <LuVolume2 size={18} />}
        </button>

        <button
          onClick={downloadAudio}
          className="ml-auto p-2 text-gray-700 hover:text-blue-600 transition"
          title="下载音频"
        >
          <LuDownload size={18} />
        </button>
      </div>

      {/* 进度条 */}
      <div
        ref={progressRef}
        className="relative h-2 bg-gray-300 rounded cursor-pointer"
        onClick={handleProgressClick}
      >
        <div
          className="absolute top-0 left-0 h-full rounded"
          style={{
            width: `${progressPercent}%`,
            backgroundColor: primaryColor,
          }}
        />
        <div
          className="absolute top-1/2 -translate-y-1/2 w-3 h-3 rounded-full"
          style={{
            left: `${progressPercent}%`,
            backgroundColor: primaryColor,
            marginLeft: "-6px",
          }}
        />
      </div>

      {/* 引用说明 */}
      {reference.fragment && (
        <div className="p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded text-sm text-gray-800">
          <strong>引用片段：</strong> {reference.fragment}
        </div>
      )}
    </div>
  );
};

import { createParser } from 'eventsource-parser'
import axios from 'axios'

interface SSEOptions {
  onMessage: (content: string) => void
  onError?: (error: Error) => void
  onFinish?: (finalText: string) => void
  headers?: Record<string, string>
}

async function refreshTokenAndRetry(originalHeaders: Record<string, string>) {
  try {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) throw new Error('No refresh token');
    const { data: { data } } = await axios.get(`/api/auth/refresh-token/${refreshToken}`);
    const { access_token, refresh_token } = data;

    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);

    return {
      ...originalHeaders,
      'Authorization': `Bearer ${access_token}`
    };
  } catch (error) {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    window.location.href = '/auth';
    throw error;
  }
}

export const streamSSEReport = async (
  endpoint: string,
  payload: Record<string, any>,
  options: SSEOptions
): Promise<() => void> => {
  const controller = new AbortController()
  let final_report = ''
  let retryCount = 0;

  const makeRequest = async (headers: Record<string, string>) => {
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...headers,
          ...options.headers
        },
        body: JSON.stringify(payload),
        signal: controller.signal
      })

      if ((response.status === 403 || response.status === 401) && retryCount === 0) {
        retryCount++;
        const newHeaders = await refreshTokenAndRetry(headers);
        return makeRequest(newHeaders);
      }

      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
      const reader = response.body?.getReader()
      if (!reader) throw new Error('Failed to get stream reader')

      const decoder = new TextDecoder()
      const parser = createParser({
        onEvent(event) {
          try {
            const data = JSON.parse(event.data)
            if (data.content) {
              final_report += data.content
            }
            options.onMessage(final_report)
          } catch (e) {
            console.error('SSE data parse failed:', e)
            options.onError?.(e instanceof Error ? e : new Error('SSE data parse failed'))
          }
        }
      })

      const pump = async () => {
        while (true) {
          const { done, value } = await reader.read()
          if (done) {
            try {
              options.onFinish?.(final_report)
            } finally {
              setTimeout(() => controller.abort(), 100)
            }
            break;
          }
          parser.feed(decoder.decode(value))
        }
      }

      pump().catch(e => options.onError?.(e))
    } catch (error) {
      options.onError?.(error instanceof Error ? error : new Error('Stream failed'))
    }
  }

  const initialHeaders = {
    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
  };

  await makeRequest(initialHeaders);
  return () => controller.abort()
}
import React from 'react';
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from '@/lib/utils';

const SiderbarSkeleton = ({ className }: { className?: string }) => {
  return (
    <div className={cn('flex flex-col w-[17.5rem] h-full p-4 bg-[#F3F4F6]', className)}>
      <div className='flex items-center mb-4 space-x-2'>
        <Skeleton className="h-10 w-10 rounded-full bg-[#EAEBED]" />
        <Skeleton className="h-6 w-12 bg-[#EAEBED]" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-10 w-[250px] bg-[#EAEBED]" />
        <div className='flex items-end flex-col space-y-2 w-full'>
          {
            Array.from({ length: 5 }).map((item, index) => (
              <Skeleton key={index} className="h-6 w-[200px] bg-[#EAEBED]" />
            ))
          }
        </div>
      </div>
    </div>
  );
}

export default SiderbarSkeleton;

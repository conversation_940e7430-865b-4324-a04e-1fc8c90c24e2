## 项目简介

ef-ceo-ai-assistant-frontend 是一个基于 Next.js 和 React 的前端项目，旨在为 CEO 提供 AI 助手功能。项目使用了多种现代前端技术栈，提供了丰富的 UI 组件和状态管理功能，支持实时数据可视化、SQL查询结果展示、Markdown渲染等功能。

主要特性：
- 实时对话交互界面
- 数据可视化图表展示
- SQL查询结果表格展示
- Markdown格式内容渲染
- 响应式设计，适配移动端和桌面端
- 国际化支持
- 飞书SDK集成

## 技术栈

### 核心框架
- **Next.js**: 15.2.3 (App Router模式)
- **React**: 19.0.0

### UI组件库
- **Ant Design**: 企业级UI组件库
- **Radix UI**: 无样式基础组件
- **Lucide React**: 图标库

### 样式处理
- **Tailwind CSS**: 实用优先的CSS框架
- **SCSS**: 复杂样式处理
- **CSS Modules**: 组件级样式隔离

### 状态管理
- **Zustand**: 轻量级状态管理
- **Immer**: 不可变数据操作

### 数据处理
- **ECharts**: 数据可视化
- **React Syntax Highlighter**: 代码高亮
- **React Markdown**: Markdown渲染
- **Axios**: HTTP客户端

### 其他工具
- **i18next**: 国际化
- **Lodash**: 实用工具库
- **copy-to-clipboard**: 剪贴板操作
- **eventsource-parser**: SSE数据处理

## 安装步骤

### 前置要求
- Node.js 18+
- pnpm 8+

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 构建生产环境
```bash
pnpm build
pnpm start
```

### 状态管理
项目使用 Zustand 进行全局状态管理，主要状态存储在 store/useConversion.ts 中。

使用示例：
```typescript
import { useConversionStore } from '@/store/useConversion';

const { conversions, curConversionId } = useConversionStore();
```

### 样式规范
- 优先使用 Tailwind CSS 实用类
- 复杂样式使用 SCSS 编写在 app/styles 目录下
- 组件级样式使用 CSS Modules

### 组件开发
核心组件位于 components 目录下：
- chat/: 聊天相关组件
- custom/: 自定义通用组件
- ui/: 基础UI组件

### 代码规范
- 使用 TypeScript 严格模式
- 组件使用 React.memo 优化性能
- 复杂逻辑使用自定义 hooks
- 使用 Prettier 和 ESLint 保证代码风格一致
- 使用 TypeScript 严格模式
- 组件使用 React.memo 优化性能
- 复杂逻辑使用自定义 hooks
- 使用 Prettier 和 ESLint 保证代码风格一致化

### 项目结构
ef-ceo-ai-assistant-frontend/
├── app/                  # Next.js App Router
├── components/           # 可复用组件
├── config/               # 应用配置
├── hooks/                # 自定义hooks
├── lib/                  # 工具函数
├── public/               # 静态资源
├── services/             # API服务
├── store/                # 状态管理
├── styles/               # 全局样式
├── .env.development      # 开发环境变量示例
├── Dockerfile            # Docker配置
├── next.config.ts        # Next.js配置
├── package.json          # 依赖管理
├── postcss.config.mjs    # PostCSS配置
└── tailwind.config.js    # Tailwind配置
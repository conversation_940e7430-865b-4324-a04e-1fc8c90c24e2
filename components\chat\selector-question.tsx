"use client";

import {
  DropdownMenu,
  Dropdown<PERSON>enuContent,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { DropdownMenuItem } from "@radix-ui/react-dropdown-menu";
import { useState } from "react";
import { useDataSet } from "@/context/data-set";
import { cn } from "@/lib/utils";
import { useConversionStore } from "@/store/useConversion";
import { shallow } from "zustand/shallow";
import { useTranslation } from "react-i18next";

import {
  TbBasketSearch,
  TbBookmark,
  TbDatabase,
  TbGlobe,
  TbChartPie,
  TbBook,
  TbWorld,
  TbBrain,
} from "react-icons/tb";

const iconConfigs = [
  {
    icon: TbDatabase,
    color: "#6366F1",
    name: "dataset",
  },
  {
    icon: TbBook,
    color: "#8B5CF6",
    name: "kb",
  },
  {
    icon: TbWorld,
    color: "#10B981",
    name: "web",
  },
  {
    icon: TbBrain,
    color: "#F59E0B",
    name: "deep",
  },
];

export const SelectorQuestion = () => {
  const { t } = useTranslation();
  const { datasetList, changeDataset, currentDataset } = useDataSet();

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const { chatType, setChatType } = useConversionStore(
    (state) => ({
      chatType: state.chatType,
      setChatType: state.setChatType,
    }),
    shallow
  );

  return (
    <>
      <div className="w-full relative flex items-center justify-between h-full mb-2">
        {/* PC端：水平排列（lg及以上屏幕显示） */}
        <div className="hidden lg:flex items-center space-x-2 mb-2">
          {iconConfigs.map((config) => {
            const IconComponent = config.icon;
            const isActive = chatType === config.name;

            return (
              <button
                key={config.name}
                className={cn(
                  "inline-flex cursor-pointer items-center gap-2 lg:px-3 px-2 py-1.5 rounded-lg lg:text-sm text-xs font-medium transition-all duration-200 relative group w-[88px] lg:w-auto lg:min-w-[110px] lg:max-w-[170px] justify-start shrink-0",
                  isActive
                    ? "bg-blue-600 text-white shadow-md border border-blue-600"
                    : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md"
                )}
                onClick={() => setChatType(isActive ? "default" : config.name)}
              >
                {/* 图标容器 */}
                <div
                  className={cn(
                    "flex items-center justify-center transition-all duration-200",
                    isActive ? "text-white" : "group-hover:scale-105"
                  )}
                >
                  <IconComponent
                    className={cn(
                      "w-4 h-4 transition-all duration-200",
                      isActive ? "text-white" : "group-hover:scale-110"
                    )}
                    style={!isActive ? { color: config.color } : {}}
                  />
                </div>

                {/* 文本 */}
                <span
                  className={cn(
                    "transition-all duration-200 truncate whitespace-nowrap max-w-[7.5rem] lg:max-w-[8.5rem]",
                    isActive ? "text-white" : "text-gray-700"
                  )}
                >
                  {t(`common.chatType.${config.name}`)}
                </span>
              </button>
            );
          })}
        </div>

        {/* 移动端：下拉选择（lg以下屏幕显示） */}
        <div className="lg:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="flex items-center gap-2 px-3 py-1.5 rounded-lg border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 shadow-sm hover:shadow-md transition-all duration-200 max-w-[200px]">
                {(() => {
                  const currentConfig =
                    iconConfigs.find((config) => config.name === chatType) ||
                    iconConfigs[0];
                  const CurrentIcon = currentConfig.icon;
                  return (
                    <>
                      <CurrentIcon
                        className="w-4 h-4"
                        style={{ color: currentConfig.color }}
                      />
                      <span className="text-xs truncate">
                        {t(`common.chatType.${currentConfig.name}`)}
                      </span>
                    </>
                  );
                })()}
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-[200px]">
              {iconConfigs.map((config) => {
                const IconComponent = config.icon;
                const isActive = chatType === config.name;
                return (
                  <DropdownMenuItem
                    key={config.name}
                    onClick={() => setChatType(config.name)}
                    className={cn(
                      "flex items-center gap-2 px-3 py-2 cursor-pointer text-sm",
                      isActive && "bg-blue-100 text-blue-600"
                    )}
                  >
                    <IconComponent
                      className="w-4 h-4"
                      style={{ color: config.color }}
                    />
                    <span>{t(`common.chatType.${config.name}`)}</span>
                  </DropdownMenuItem>
                );
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </>
  );
};

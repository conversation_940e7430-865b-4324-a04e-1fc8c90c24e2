import { fetchAuthCode } from "@/services/api";

export const dingding = () => {
  return new Promise((resolve) => {
    dd.ready(() => {
      console.log("钉钉 JSAPI 初始化成功");
      const currentUrl = window.location.href;
      // 从URL中解析出corpId（假设URL中已被替换为真实corpId值）
      const urlParams = new URLSearchParams(currentUrl.split('?')[1]);
      const corpId = urlParams.get('corpId');
      console.log("🚀 ~ corpId:", corpId);

      dd.runtime.permission.requestAuthCode({
        corpId: corpId || "ding12345xxx", // 优先使用URL中的corpId
        onSuccess: async (info) => {
          const code = info.code; // 通过该免登授权码可以获取用户身份
          try {
            const { data } = await fetchAuthCode(code, 'dingtalk');
            console.log("🚀 ~ onSuccess: ~ data:", data)
            const access_token = data.access_token;
            const refresh_token = data.refresh_token;
            localStorage.setItem('access_token', access_token);
            localStorage.setItem('refresh_token', refresh_token);
            resolve(true);
          } catch (error) {
            message.error('获取钉钉授权失败');
            resolve(false);
          }
        },
        onFail: (err) => {
          message.error('获取钉钉授权失败');
          console.error("获取免登授权码失败:", err);
          resolve(false);
        }
      });
    });

    dd.error((err) => {
      console.error("钉钉 JSAPI 初始化失败:", err);
      resolve(false);
    });
  });
};
import { produce } from "immer";
import { createWithEqualityFn } from "zustand/traditional";

interface SelectorThemeState {
  themeList: any[],
  currentThemeName: string
  currentThemeId: number
  questionList: any[]
}

interface SelectorThemeAction {
  getThemeList: () => void
  setCurrentTheme: (id: number) => void
  fetchQuesitonList: (id: number) => Promise<Array<{ id: number; content: string }>>
}

const emojiList = ['😊', '🤔', '📊', '🎯', '💡', '✨'];

const initSelectorThemeData = {
  themeList: [],
  currentThemeName: '智能选择',
  currentThemeId: 0,
  questionList: [],
}

type SelectorThemeStore = SelectorThemeState & SelectorThemeAction

export const useSelectorThemeStore = createWithEqualityFn(
  (set, get): SelectorThemeStore => (
    {
      ...initSelectorThemeData,
      getThemeList: () => {
        const themeList = [
          { id: 0, name: '智能选择' },
          { id: 1, name: '薪酬与编制分析' },
          { id: 2, name: '员工离职流程管理' },
          { id: 3, name: '组织架构与岗位体系' },
          { id: 4, name: '员工人事管理' },
        ] as any

        set(produce(state => {
          state.themeList = themeList
        }))
      },
      fetchQuesitonList: (id: number) => {
        const questionMap = {
          1: [ // 薪酬与编制分析
            '公司各部门的平均薪资水平是多少？',
            '本年度薪酬预算执行情况如何？',
            '各岗位职级的人员编制情况是怎样的？'
          ],
          2: [ // 员工离职流程管理
            '近半年的员工离职率趋势如何？',
            '主要离职原因分布是什么？',
            '各部门的离职率排名如何？'
          ],
          3: [ // 组织架构与岗位体系
            '公司的组织架构层级分布是怎样的？',
            '各部门的人员岗位配置情况如何？',
            '关键岗位的人才储备情况是怎样的？'
          ],
          4: [ // 员工人事管理
            '员工的年龄和司龄分布是怎样的？',
            '本月员工考勤异常情况统计如何？',
            '员工的学历和职称分布是怎样的？'
          ],
        } as any

        const questions = questionMap[id]?.map((content: string, index: number) => ({
          id: index + 1,
          content
        })) || [];

        return Promise.resolve(questions);
      },
      setCurrentTheme: async (id: number) => {
        const theme = get().themeList.find((item: any) => item.id === id)
        // const questions = await get().fetchQuesitonList(id); // 获取薪酬与编制分析相关的问题
        // const questionsWithEmoji = questions.map((q, index) => ({
        //   ...q,
        //   emoji: emojiList[index % emojiList.length]
        // }));
        set(produce(state => {
          state.currentThemeName = theme.name
          state.currentThemeId = id
          // state.questionList = questionsWithEmoji
        }))
      },
    }
  )
)
/**
 * 思维链数据处理工具
 * 基于 planMockData 数据结构设计
 */

export interface ResearchResult {
  stage: 'planning' | 'rewriting' | 'execution' | 'analysis' | 'completion';
  thought_number: number;
  next_step_needed: boolean;
  research_plan?: any;
  required_tools?: any[];
  tool_executions?: any[];
  deep_analysis_result?: any;
  [key: string]: any;
}

export interface ProcessedThought {
  thought_number: number;
  stages: Record<string, ResearchResult>;
  currentStage: string;
  title: string;
  content: string;
  next_step_needed: boolean;
  status: 'pending' | 'thinking' | 'completed';
  stageData: {
    planning?: any;
    rewriting?: any;
    execution?: any;
    analysis?: any;
  };
}

export interface ThoughtChainData {
  stages: Record<number, Record<string, ResearchResult>>;
  currentStage: string;
  currentThoughtNumber: number;
  processedThoughts: ProcessedThought[];
}

/**
 * 处理单个研究结果数据
 */
export function processResearchResult(
  existingData: ThoughtChainData | null,
  newResearchResult: ResearchResult
): ThoughtChainData | { isCompletion: true; data: ResearchResult } {
  const { stage, thought_number, next_step_needed } = newResearchResult;
  console.log(stage, 'stage');

  // 如果是 completion 阶段，返回特殊标识，不计入思维链
  if (stage === 'completion') {
    return {
      isCompletion: true,
      data: newResearchResult
    };
  }

  // 初始化数据结构
  const data: ThoughtChainData = existingData || {
    stages: {},
    currentStage: stage,
    currentThoughtNumber: thought_number,
    processedThoughts: []
  };

  // 更新当前状态
  data.currentStage = stage;
  data.currentThoughtNumber = thought_number;

  // 存储阶段数据
  if (!data.stages[thought_number]) {
    data.stages[thought_number] = {};
  }
  data.stages[thought_number][stage] = {
    ...newResearchResult,
    timestamp: Date.now()
  } as ResearchResult;

  // 更新处理后的思考数据
  data.processedThoughts = generateProcessedThoughts(data.stages);

  return data;
}

/**
 * 从阶段数据生成处理后的思考链
 */
function generateProcessedThoughts(stages: Record<number, Record<string, ResearchResult>>): ProcessedThought[] {
  const thoughts: ProcessedThought[] = [];

  // 按 thought_number 排序
  const sortedThoughtNumbers = Object.keys(stages)
    .map(Number)
    .sort((a, b) => a - b);

  for (const thoughtNumber of sortedThoughtNumbers) {
    const stageData = stages[thoughtNumber];
    const stageNames = Object.keys(stageData);

    // 确定当前阶段（取最新的阶段）
    const currentStage = stageNames.reduce((latest, current) => {
      const latestTimestamp = (stageData[latest] as any)?.timestamp || 0;
      const currentTimestamp = (stageData[current] as any)?.timestamp || 0;
      return currentTimestamp > latestTimestamp ? current : latest;
    });

    const currentStageData = stageData[currentStage];

    const thought: ProcessedThought = {
      thought_number: thoughtNumber,
      stages: stageData,
      currentStage,
      title: getStageDisplayName(currentStage),
      content: generateThoughtContent(currentStage, currentStageData),
      next_step_needed: currentStageData.next_step_needed || false,
      status: determineThoughtStatus(stageData, currentStageData),
      stageData: {
        planning: stageData.planning,
        rewriting: stageData.rewriting,
        execution: stageData.execution,
        analysis: stageData.analysis,
      }
    };

    thoughts.push(thought);
  }

  return thoughts;
}

/**
 * 获取阶段的显示名称
 */
function getStageDisplayName(stage: string): string {
  const stageNames: Record<string, string> = {
    'analysis': '深度分析',
    'rewriting': '查询重写',
    'execution': '执行查询',
    'synthesis': '结果综合',
    'planning': '制定计划',
    'validation': '结果验证'
  };

  return stageNames[stage] || stage;
}

/**
 * 生成思考内容
 */
function generateThoughtContent(stage: string, stageData: ResearchResult): string {
  switch (stage) {
    case 'planning':
      if (stageData.research_plan?.overall_goal) {
        return `目标: ${stageData.research_plan.overall_goal}`;
      }
      return '正在制定研究计划...';

    case 'rewriting':
      if (stageData.required_tools?.length) {
        return `需要执行 ${stageData.required_tools.length} 个工具查询：\n${stageData.required_tools
            .map((tool: any, index: number) => `${index + 1}. ${tool.tool_name}: ${tool.parameters?.query || ''}`)
            .join('\n')
          }`;
      }
      return '正在重写查询...';

    case 'execution':
      if (stageData.tool_executions?.length) {
        return `已执行 ${stageData.tool_executions.length} 个工具查询`;
      }
      return '正在执行数据查询...';

    case 'analysis':
      if (stageData.deep_analysis_result?.analysis_findings?.key_insights) {
        return stageData.deep_analysis_result.analysis_findings.key_insights
          .map((insight: any) => insight.insight)
          .join('\n');
      }
      return '正在进行深度分析...';

    default:
      return `正在执行 ${stage} 阶段...`;
  }
}

/**
 * 确定思考状态
 */
function determineThoughtStatus(
  stageData: Record<string, ResearchResult>,
  currentStageData: ResearchResult
): 'pending' | 'thinking' | 'completed' {
  // 如果有多个阶段且当前阶段不需要下一步，则认为已完成
  const stageCount = Object.keys(stageData).length;

  if (!currentStageData.next_step_needed) {
    return 'completed';
  }

  if (stageCount > 1) {
    return 'thinking';
  }

  return 'pending';
}

/**
 * 将处理后的思考数据转换为组件所需的格式
 */
export function convertToThoughtSteps(processedThoughts: ProcessedThought[]): any[] {
  return processedThoughts.map((thought, index) => ({
    id: `thought-${thought.thought_number}`,
    title: thought.title,
    content: thought.content,
    status: thought.status,
    duration: '',
    learnings: '',
    stageData: thought.stageData,
    // 保留原始数据以供调试
    rawData: thought
  }));
}

/**
 * 获取当前活跃的思考步骤
 */
export function getCurrentActiveThought(data: ThoughtChainData): ProcessedThought | null {
  if (!data.processedThoughts.length) return null;

  // 返回当前 thought_number 对应的思考
  return data.processedThoughts.find(
    thought => thought.thought_number === data.currentThoughtNumber
  ) || data.processedThoughts[data.processedThoughts.length - 1];
}

'use client'

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ReportData, useReportStore } from '@/store/useReport';

export const ReportPanel: React.FC = () => {
  const { currentReport, updatingMessageId, getReportByMessageId, isReportLoading } = useReportStore();

  const targetMessageId = currentReport?.msgId;

  const effectiveReport: ReportData | null = useMemo(() => {
    if (!targetMessageId) return currentReport;
    if (updatingMessageId && updatingMessageId === targetMessageId) return currentReport;
    const cached = getReportByMessageId(targetMessageId);
    if (cached) return cached;
    if (currentReport && currentReport.msgId === targetMessageId) return currentReport;
    return null;
  }, [currentReport, updatingMessageId, getReportByMessageId, targetMessageId]);

  const [isIframeLoading, setIsIframeLoading] = useState(true);
  const handleIframeLoad = () => setIsIframeLoading(false);

  const tocUrl = effectiveReport?.tocUrl;

  useEffect(() => {
    // 每次切换报告或加载新链接时，重新展示 loading
    setIsIframeLoading(true);
  }, [tocUrl]);

  return (
    <div className="w-full h-full flex flex-col relative">
      <div className="flex-1 overflow-hidden">
        {!tocUrl ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-50">
            <div className="text-center text-gray-500">
              <div className="text-lg mb-2">暂无报告内容</div>
              <div className="text-sm">报告生成完成后将在此显示</div>
            </div>
          </div>
        ) : (
          <div className="relative w-full h-full">
            {isIframeLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-white z-20">
                <div className="flex flex-col items-center space-y-3">
                  <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <div className="text-sm text-gray-500">正在加载报告...</div>
                </div>
              </div>
            )}
            <iframe
              className="w-full h-full border-0"
              title="报告内容"
              src={tocUrl}
              sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
              onLoad={handleIframeLoad}
              referrerPolicy="no-referrer"
            />
          </div>
        )}
      </div>
    </div>
  );
};
'use client'

import ChatContent from '@/components/chat/chat-content';
import TextareaInput from '@/components/chat/textarea-input';
import Welcome from '@/components/chat/welcome';
import Header from '@/components/custom/header';
import Loading from '@/components/custom/loading/loading';
import { useConversionStore } from '@/store/useConversion';
import React, { useCallback, useMemo, useState } from 'react';
import { shallow } from 'zustand/shallow';
import ScrollToBottom from 'react-scroll-to-bottom';
import { Splitter } from 'antd';
import { Reference } from '@/components/reference';
import { useReferenceStore } from '@/store/useReference';

const Chat = () => {
  const { isChatStarted, conversions, curConversionId, messageLoading, isResponding } = useConversionStore((state) => ({
    isChatStarted: state.isChatStarted,
    messageLoading: state.messageLoading,
    conversions: state.conversions,
    curConversionId: state.curConversionId,
    isResponding: state.isResponding
  }), shallow)

  const { openReference } = useReferenceStore((state) => ({
    openReference: state.openReference,
  }), shallow);

  const [scrollToBottom, setScrollToBottom] = useState<(() => void) | null>(null);
  const [isSticky, setIsSticky] = useState(true);

  const handleScrollToBottomReady = useCallback((scrollFn: () => void) => {
    setScrollToBottom(() => scrollFn);
  }, []);

  const handleStickyChange = useCallback((sticky: boolean) => {
    setIsSticky(sticky);
  }, []);

  const curConversion = useMemo(() => {
    return conversions.find((item) => item.id === curConversionId)
  }, [conversions, curConversionId])

  if (messageLoading) {
    return (
      <div className='w-full h-full flex items-center justify-center'>
        <Loading />
      </div>
    )
  }

  return (
    <Splitter >
      <Splitter.Panel defaultSize="55%" min="20%" max="70%" resizable={false}>
        <div className='flex flex-col h-full w-full'>
          <ScrollToBottom
            initialScrollBehavior="auto"
            className='flex-1 overflow-auto no-scrollbar'
            followButtonClassName='hidden'
          >
            {!isChatStarted ? (
              <Welcome />
            ) : (
              <>
                <Header />
                <div className='flex-grow'>
                  <ChatContent list={curConversion?.messages as any[]} onStickyChange={handleStickyChange} onScrollToBottomReady={handleScrollToBottomReady} />
                </div>
              </>
            )}
          </ScrollToBottom>
          {
            isChatStarted && <TextareaInput isBottom={isSticky} scrollToBottom={scrollToBottom || (() => { })} />
          }
        </div>
      </Splitter.Panel>
      {openReference && (
        <Splitter.Panel resizable={false}>
          <Reference />
        </Splitter.Panel>
      )}
    </Splitter>
  );
}

export default Chat;
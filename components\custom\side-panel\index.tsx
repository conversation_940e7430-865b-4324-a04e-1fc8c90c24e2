import { Reference } from "@/components/reference";
import { useSidePanelStore } from "@/store/useSidePanel";
import { IoMdClose } from "react-icons/io";
import { shallow } from "zustand/shallow";
import { WebSearchPanel } from "./web-search-panel";
import { ReportPanel } from "./report-panel";

export const SidePanel = () => {
  const { isOpen, panelType, closeSidePanel } = useSidePanelStore(
    (state) => ({
      isOpen: state.isOpen,
      panelType: state.panelType,
      closeSidePanel: state.closeSidePanel,
    }),
    shallow
  );

  if (!isOpen) return null;

  // 根据面板类型展示不同的内容
  const renderPanelContent = () => {
    switch (panelType) {
      case 'reference':
        return <Reference />
      case 'web-search':
        return <WebSearchPanel />
      case 'report':
        return <ReportPanel />
      default:
        return <div className="p-4 text-gray-400">未知面板类型</div>;
    }
  }

  // 根据面板类型获取标题
  const getPanelTitle = () => {
    switch (panelType) {
      case 'reference':
        return '知识库原文引用';
      case 'web-search':
        return '网页搜索结果';
      case 'report':
        return '分析报告';
      default:
        return '详情面板';
    }
  };

  return (
    <div className="w-full flex flex-col h-full border-l border-gray-200">
      <div className="w-full px-4 flex justify-between items-center border-b h-14 border-gray-200">
        <div className="text-lg font-bold">{getPanelTitle()}</div>
        <button
          className="cursor-pointer rounded-full hover:bg-gray-200 text-gray-400 hover:text-gray-600 transition-colors z-10"
          onClick={closeSidePanel}
          aria-label="关闭面板"
        >
          <IoMdClose size={20} />
        </button>
      </div>
      <div className="w-full h-full overflow-y-auto flex-1">
        {renderPanelContent()}
      </div>
    </div>
  );
}
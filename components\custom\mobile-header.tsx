import React, { useState } from 'react';
import { AiOutlineMenuUnfold } from "react-icons/ai";
import { TbMessagePlus } from "react-icons/tb";
import MobileMenu from './mobile-menu';
import { useConversionStore } from '@/store/useConversion';
import { shallow } from 'zustand/shallow';

const MobileHeader = () => {
  const { changeConversion, isMobileCollapsed, setIsMobileCollapsed, systemConfig } = useConversionStore((state) => ({
    changeConversion: state.changeConversion,
    isMobileCollapsed: state.isMobileCollapsed,
    setIsMobileCollapsed: state.setIsMobileCollapsed,
    systemConfig: state.systemConfig,
  }), shallow)

  return (
    <>
      <div className='h-10 flex items-center justify-between lg:hidden bg-white'>
        <div onClick={() => setIsMobileCollapsed(false)}>
          <AiOutlineMenuUnfold className='text-2xl' />
        </div>
        <div className='flex items-center'>
          <img src="/robat.png" alt="" className='w-6 h-6'/>
          <span className='ml-2 text-[1.25rem] font-[600] text-[#3B3B39]'>{systemConfig?.system_name || '智能问数'}</span>
        </div>
        <div onClick={() => changeConversion('-1')}>
          <TbMessagePlus className='text-2xl' />
        </div>
      </div >
    </>
  );
}

export default MobileHeader;

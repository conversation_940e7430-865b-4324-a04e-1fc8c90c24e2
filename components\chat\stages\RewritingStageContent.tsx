/**
 * Rewriting 阶段内容组件（与 Execution 风格对齐）
 */

import { mapToolName } from '@/utils/mapToolName';
import React, { useMemo, useState } from 'react';
import { Collapse, Modal, Tooltip } from 'antd';

interface RewritingStageContentProps {
  stageData: any;
}

const SectionTitle: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <h4 className="mb-2 text-sm font-semibold text-[#334155]">{children}</h4>
);

const RewritingStageContent: React.FC<RewritingStageContentProps> = ({ stageData }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalData, setModalData] = useState<{
    title: string;
    params: any;
    toolName: string;
  } | null>(null);

  if (!stageData) {
    return <div className="text-sm text-[#64748B]">无重写数据</div>;
  }

  const tools = stageData.required_tools || [];
  if (tools.length === 0) {
    return <div className="text-sm text-[#64748B]">无需要执行的工具</div>;
  }

  // 分组：按工具名聚合
  const grouped = useMemo(() => {
    const acc: Record<string, any[]> = {};
    for (const t of tools) {
      const name = (t?.tool_name || '未知工具') as string;
      if (!acc[name]) acc[name] = [];
      acc[name].push(t);
    }
    return acc;
  }, [tools]);

  const getToolBg = (toolName: string) => {
    const k = (toolName || '未知工具').toLowerCase();
    if (k.includes('nl2sql')) return 'bg-[#EEF2FF] hover:bg-[#E0E7FF] border-[#C7D2FE]';
    if (k.includes('search')) return 'bg-[#ECFDF5] hover:bg-[#D1FAE5] border-[#A7F3D0]';
    if (k.includes('crawler') || k.includes('crawl')) return 'bg-[#FFFBEB] hover:bg-[#FEF3C7] border-[#FDE68A]';
    if (k.includes('excel') || k.includes('sheet')) return 'bg-[#F0FDFA] hover:bg-[#CCFBF1] border-[#99F6E4]';
    if (k.includes('code')) return 'bg-[#FAF5FF] hover:bg-[#F3E8FF] border-[#E9D5FF]';
    if (k.includes('query_knowledge_base') || k.includes('knowledge')) return 'bg-[#ECFEFF] hover:bg-[#CFFAFE] border-[#A5F3FC]';
    return 'bg-[#F8FAFC] hover:bg-[#F1F5F9] border-[#E2E8F0]';
  };

  const handleShowParams = (tool: any, groupName: string) => {
    setModalData({
      title: tool?.parameters?.query || '工具参数',
      params: tool?.parameters || {},
      toolName: groupName,
    });
    setModalVisible(true);
  };

  return (
    <div className="space-y-4">
      <SectionTitle>计划执行（所需工具） ({tools.length})</SectionTitle>

      <Collapse
        bordered={false}
        defaultActiveKey={Object.keys(grouped).map((n) => mapToolName(n))}
        className="bg-white"
        items={Object.entries(grouped).map(([toolName, list]) => ({
          key: mapToolName(toolName),
          label: (
            <span className="flex items-center justify-between w-full">
              <span className="font-medium text-[#334155]">{mapToolName(toolName)}</span>
              <span className="text-xs text-[#64748B]">{list.length} 个任务</span>
            </span>
          ),
          children: (
            <div className="flex flex-wrap gap-2">
              {list.map((tool: any, idx: number) => {
                const bg = getToolBg(toolName);
                const hasQuery = !!tool?.parameters?.query;
                const content = (
                  <div
                    className={`border ${bg} rounded px-2 py-1 text-xs text-[#334155] ${hasQuery ? 'cursor-pointer' : 'cursor-default'}`}
                    style={{ width: 100 }}
                    onClick={() => handleShowParams(tool, mapToolName(toolName))}
                  >
                    <div className="font-mono whitespace-nowrap overflow-hidden text-ellipsis" title="">
                      {tool?.parameters?.query || tool?.description || '（无查询语句）'}
                    </div>
                    <div className="mt-1 text-[10px] text-[#6366F1]">查看参数 →</div>
                  </div>
                );

                return (
                  <Tooltip key={`${toolName}-${idx}`} title={tool?.parameters?.query || tool?.description} mouseEnterDelay={0.2}>
                    {content}
                  </Tooltip>
                );
              })}
            </div>
          )
        }))}
      />

      {/* 参数弹窗 */}
      <Modal
        title={
          <div>
            <div className="font-medium">{modalData?.toolName}</div>
            <div className="text-sm text-gray-500 font-normal mt-1">
              {modalData?.title}
            </div>
          </div>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width="60%"
        style={{ maxWidth: '900px' }}
        styles={{ body: { maxHeight: '60vh', overflow: 'auto' } }}
      >
        {modalData?.params && (
          <div className="text-xs space-y-2">
            {Object.entries(modalData.params).map(([k, v]) => (
              <div key={k} className="flex">
                <div className="w-32 text-[#475569] font-medium">{k}</div>
                <div className="flex-1 text-[#334155]">
                  {typeof v === 'object' ? JSON.stringify(v) : String(v)}
                </div>
              </div>
            ))}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default RewritingStageContent;

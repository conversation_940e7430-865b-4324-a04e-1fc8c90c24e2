'use client';

import React, { memo, useState } from 'react';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { vs } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { RiFileCopyLine } from 'react-icons/ri';
import { AiOutlineCheck } from 'react-icons/ai'; // 用于复制成功的图标
import copy from 'copy-to-clipboard';

interface SimpleHtmlMarkdownProps {
  htmlContent: string;
}

const CustomPreTag = ({ children, htmlContent, ...props }: any) => {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = () => {
    if (isCopied) return;
    if (htmlContent) {
      copy(htmlContent);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000); // 2秒后重置
    }
  };

  return (
    <div className="relative group"> {/* 添加相对定位容器 */}
      <pre
        {...props}
        className="p-4 m-0 rounded-t-none rounded-b-md overflow-auto" // 覆盖默认 padding/margin, 移除顶部圆角
      >
        {children}
      </pre>
      <button
        onClick={handleCopy}
        className={`absolute cursor-pointer top-2 right-2 flex items-center text-xs px-2 py-1 rounded transition-all duration-200 ${isCopied
          ? 'bg-green-100 text-green-700' // 成功状态
          : 'bg-gray-200 text-gray-700 hover:bg-gray-300 opacity-0 group-hover:opacity-100' // 默认/悬停状态
          }`}
        title={isCopied ? "已复制!" : "复制代码"}
      >
        {isCopied ? (
          <>
            <AiOutlineCheck className="w-3 h-3 mr-1" />
            已复制
          </>
        ) : (
          <>
            <RiFileCopyLine className="w-3 h-3 mr-1" />
            复制
          </>
        )}
      </button>
    </div>
  );
};

const SimpleHtmlMarkdown = memo(({ htmlContent }: SimpleHtmlMarkdownProps) => {
  return (
    <SyntaxHighlighter
      language="html"
      style={vs}
      customStyle={{
        margin: 0,
        padding: '1rem',
        backgroundColor: '#F9FAFB',
        fontSize: '0.875rem', // text-sm
        lineHeight: '1.25rem', // leading-5
      }}
      showLineNumbers={false} // 可选：关闭行号以提升性能
      wrapLines={true}
      wrapLongLines={true} // 关键：自动换行长行
      PreTag={(props) => <CustomPreTag {...props} htmlContent={htmlContent} />}
    >
      {htmlContent}
    </SyntaxHighlighter>
  );
});

SimpleHtmlMarkdown.displayName = 'SimpleHtmlMarkdown';

export default SimpleHtmlMarkdown;

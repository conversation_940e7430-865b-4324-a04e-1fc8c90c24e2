{"name": "zh-chat", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "~5.6.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@formatjs/intl-localematcher": "^0.6.1", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@types/dompurify": "^3.2.0", "antd": "^5.24.5", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "dompurify": "^3.2.6", "echarts-for-react": "^3.0.3", "eventsource-parser": "^3.0.0", "framer-motion": "^12.23.7", "hastscript": "^9.0.1", "i18next": "^24.2.3", "i18next-resources-to-backend": "^1.2.1", "idb-keyval": "^6.2.1", "immer": "^10.1.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "katex": "^0.16.21", "lodash": "^4.17.21", "lucide-react": "^0.482.0", "mermaid": "^11.9.0", "modern-screenshot": "^4.6.5", "negotiator": "^1.0.0", "next": "15.2.3", "pdfjs-dist": "4.8.69", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-pdf": "9.2.1", "react-scroll-to-bottom": "^4.2.0", "react-string-replace": "^1.1.1", "react-syntax-highlighter": "^15.6.1", "react-virtuoso": "^4.12.5", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-react": "^8.0.0", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "sass": "^1.86.0", "styled-components": "^6.1.18", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "unist-util-visit-parents": "^6.0.1", "use-context-selector": "^2.0.0", "zustand": "4.5.6"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-scroll-to-bottom": "^4.2.5", "@types/react-syntax-highlighter": "^15.5.13", "code-inspector-plugin": "^0.20.6", "cross-env": "^7.0.3", "tailwindcss": "^4", "typescript": "^5"}}
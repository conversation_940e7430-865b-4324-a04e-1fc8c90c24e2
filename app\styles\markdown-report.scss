/*
 * Report Theme SCSS
 *
 * This SCSS file provides a custom theme for a Markdown report,
 * inspired by the provided images and designed to be clean and professional.
 */

// Define the light theme variables
@mixin light {
  // Color scheme
  color-scheme: light;

  // Primary text and background colors
  --color-text-primary: #101828;
  --color-bg-body: #ffffff;
  --color-bg-highlight: #f9fafa;
  --color-border: #e0e0e0;
  --color-heading: #101828;
  --color-accent: #007bff; // A nice blue for links, etc.
  --color-link: #0056b3; // Darker blue for visited links

  // Code block colors (similar to GitHub's light theme but for a report)
  --color-prettylights-syntax-comment: #6a737d;
  --color-prettylights-syntax-constant: #005cc5;
  --color-prettylights-syntax-entity: #6f42c1;
  --color-prettylights-syntax-storage-modifier-import: #24292f;
  --color-prettylights-syntax-entity-tag: #22863a;
  --color-prettylights-syntax-keyword: #d73a49;
  --color-prettylights-syntax-string: #032f62;
  --color-prettylights-syntax-variable: #e36209;
}

// Apply the light theme variables to the body or a root element
:root {
  @include light;
}

// Base styles for the markdown-body-report
.markdown-body-report {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  color: var(--color-text-primary);
  background-color: var(--color-bg-body);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  font-size: 16px; // Base font size
  font-weight: 400;
  line-height: 1.6;
  word-wrap: break-word;
  padding: 40px;

  // Headings
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-top: 1.2em;
    margin-bottom: 0.5em;
    color: var(--color-heading);
  }

  h1 {
    font-size: 2rem; // 32px, 原为 2.25rem
    border-bottom: 2px solid var(--color-border);
    padding-bottom: 0.3em;
  }

  h2 {
    font-size: 1.75rem; // 28px, 原为 1.875rem
    border-bottom: 1px solid var(--color-border);
    padding-bottom: 0.3em;
  }

  h3 {
    font-size: 1.375rem; // 24px
    margin-top: 2em;
  }

  h4 {
    font-size: 1.125rem; // 20px
  }

  h5 {
    font-size: 1rem; // 16px
  }

  h6 {
    font-size: 1rem; // 16px
    color: #555;
    font-weight: 400;
  }

  // Paragraphs and lists
  p {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  ul,
  ol {
    padding-left: 2em;
    margin-top: 0;
    margin-bottom: 1rem;
  }

  li+li {
    margin-top: 0.25em;
  }

  // Links
  a {
    color: var(--color-link);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  // Horizontal rule
  hr {
    height: 1px;
    padding: 0;
    margin: 24px 0;
    background-color: var(--color-border);
    border: 0;
    box-sizing: content-box;
    overflow: hidden;
  }

  // Blockquotes
  blockquote {
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
    margin: 0 0 1rem;
  }

  // Tables
  table {
    display: block;
    width: 100%;
    overflow: auto;
    border-collapse: collapse;
    margin-bottom: 1rem;
  }

  table th,
  table td {
    padding: 6px 13px;
    border: 1px solid var(--color-border);
  }

  table th {
    font-weight: 600;
    background-color: var(--color-bg-highlight);
  }

  table tr {
    background-color: var(--color-bg-body);
    border-top: 1px solid var(--color-border);
  }

  table tr:nth-child(2n) {
    background-color: var(--color-bg-highlight);
  }

  // Inline code
  code {
    font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, Courier, monospace;
    font-size: 0.85em;
    background-color: rgba(27, 31, 35, .05);
    border-radius: 6px;
    padding: 0.2em 0.4em;
    color: #d73a49;
  }

  // Block code (pre)
  pre {
    background-color: var(--color-bg-highlight);
    border: 1px solid var(--color-border);
    border-radius: 6px;
    padding: 16px;
    overflow: auto;
    line-height: 1.45;

    &>code {
      background: none;
      border: none;
      padding: 0;
      margin: 0;
      display: block;
      font-size: 1em;
      color: var(--color-prettylights-syntax-string);
      white-space: pre-wrap; // Added this to prevent horizontal overflow
      word-break: break-all; // Added this to break long words
    }
  }

  // Mermaid diagram styles
  .mermaid-container {
    position: relative;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    background-color: var(--color-bg-body);
    margin: 1rem 0;
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .mermaid-preview-hint {
        opacity: 1;
      }
    }

    .mermaid-preview-hint {
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.2s ease;
      pointer-events: none;
      z-index: 10;
    }

    svg {
      max-width: 100%;
      height: auto;
    }
  }

  // Modal styles for mermaid preview
  .mermaid-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;

    .mermaid-modal-content {
      position: relative;
      width: 95vw;
      height: 95vh;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;

      .mermaid-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px;
        border-bottom: 1px solid var(--color-border);
        background-color: var(--color-bg-highlight);
        border-radius: 8px 8px 0 0;

        .mermaid-view-controls {
          display: flex;
          align-items: center;
          gap: 8px;

          button {
            background: var(--color-bg-body);
            border: 1px solid var(--color-border);
            border-radius: 4px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.2s ease;

            &:hover {
              background-color: var(--color-bg-highlight);
              border-color: var(--color-accent);
            }

            &:active {
              transform: scale(0.95);
            }

            &.active {
              background-color: var(--color-accent);
              color: white;
              border-color: var(--color-accent);
            }
          }

          .divider {
            width: 1px;
            height: 20px;
            background-color: var(--color-border);
            margin: 0 4px;
          }

          span {
            font-size: 14px;
            font-weight: 500;
            color: var(--color-text-primary);
            min-width: 50px;
            text-align: center;
          }
        }

        .mermaid-modal-close {
          background: none;
          border: none;
          font-size: 24px;
          cursor: pointer;
          color: #666;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: #f0f0f0;
          }
        }
      }

      .mermaid-modal-body {
        flex: 1;
        overflow: auto;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--color-bg-body);
        border-radius: 0 0 8px 8px;

        .mermaid-diagram-content {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;

          &.fit {
            svg {
              max-width: 100%;
              max-height: 100%;
              width: auto;
              height: auto;
              object-fit: contain;
            }
          }

          &.actual {
            overflow: auto;
            justify-content: flex-start;
            align-items: flex-start;
            
            svg {
              max-width: none !important;
              max-height: none !important;
              width: auto !important;
              height: auto !important;
              min-width: 800px;
              min-height: 600px;
            }
          }
        }
      }
    }
  }
}
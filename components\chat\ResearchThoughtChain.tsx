/**
 * 研究思维链组件 - 左右分栏布局方案
 * 左侧：思考列表
 * 右侧：思考详情 + 阶段内容
 */

import React, { useState, useEffect } from "react";
import {
  LuChevronRight,
  <PERSON><PERSON><PERSON><PERSON>,
  LuCheck,
  LuClock,
  LuBrain,
} from "react-icons/lu";
import { type ProcessedThought } from "@/utils/thoughtChainProcessor";
import PlanningStageContent from "./stages/PlanningStageContent";
import RewritingStageContent from "./stages/RewritingStageContent";
import ExecutionStageContent from "./stages/ExecutionStageContent";
import AnalysisStageContent from "./stages/AnalysisStageContent";
import { useReportStore } from "@/store/useReport";
import { useSidePanelStore } from "@/store/useSidePanel";
import { useConversionStore } from "@/store/useConversion";
import { fetchGenerateHtmlReport } from "@/services/api";
import ReportGeneratingStatus from "./ReportGeneratingStatus";

interface ResearchThoughtChainProps {
  thoughts: ProcessedThought[];
  isResponding?: boolean;
  messageId: string;
}

const ResearchThoughtChain: React.FC<ResearchThoughtChainProps> = ({
  thoughts,
  isResponding = false,
  messageId,
}) => {
  
  const { generateReport } = useReportStore();
  const { openSidePanel } = useSidePanelStore();
  const { curConversionId, isCollapsed, changeCollapsed } = useConversionStore((state: any) => ({
    curConversionId: state.curConversionId,
    isCollapsed: state.isCollapsed,
    changeCollapsed: state.changeCollapsed,
  }));
  const [activeThought, setActiveThought] = useState<number | null>(null);
  const [activeThoughtIndex, setActiveThoughtIndex] = useState<number>(-1); // 激活思考的索引，用于滑动效果
  const [activeStage, setActiveStage] = useState<string>("planning");
  const [openToolIndex, setOpenToolIndex] = useState<number | null>(null);
  const [openTip, setOpenTip] = useState<string | null>(null);

  // 自动激活最新的思考
  useEffect(() => {
    if (thoughts && thoughts.length > 0) {
      const latestThought = thoughts[thoughts.length - 1];
      const latestIndex = thoughts.length - 1;
      setActiveThought(latestThought.thought_number);
      setActiveThoughtIndex(latestIndex);
      setActiveStage(latestThought.currentStage);
    }
  }, [thoughts]);

  // 手动点击思考时的处理
  const handleThoughtClick = (thoughtNumber: number, index: number) => {
    setActiveThought(thoughtNumber);
    setActiveThoughtIndex(index);
  };

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case "planning":
        return <LuBrain className="w-4 h-4" />;
      case "rewriting":
        return <LuClock className="w-4 h-4" />;
      case "execution":
        return <LuLoader className="w-4 h-4" />;
      case "analysis":
        return <LuCheck className="w-4 h-4" />;
      default:
        return <LuBrain className="w-4 h-4" />;
    }
  };

  if (!thoughts || thoughts.length === 0) {
    return null;
  }

  const renderStageContent = (thought: ProcessedThought, stage: string) => {
    const stageData = thought.stages[stage];
    if (!stageData) return null;

    switch (stage) {
      case "planning":
        return <PlanningStageContent stageData={stageData} />;
      case "rewriting":
        return <RewritingStageContent stageData={stageData} />;
      case "execution":
        return <ExecutionStageContent stageData={stageData} openToolIndex={openToolIndex} openTip={openTip} />;
      case "analysis":
        return (
          <AnalysisStageContent
            stageData={stageData}
            onViewExecution={(toolIdx, tip) => {
              setOpenToolIndex(toolIdx);
              setOpenTip(tip ?? null);
              setActiveStage('execution');
            }}
          />
        );
      default:
        return (
          <div className="text-sm text-gray-600">
            <pre className="whitespace-pre-wrap text-xs">
              {JSON.stringify(stageData, null, 2)}
            </pre>
          </div>
        );
    }
  };

  const selectedThought = thoughts.find(
    (t) => t.thought_number === activeThought
  );

  const handleViewReport = async () => {
    if (!curConversionId) return;
    await generateReport(messageId, curConversionId);
    openSidePanel('report');
    if (!isCollapsed) changeCollapsed();
  };

  return (
    <>
      <div className="bg-[#FAFBFF] rounded-lg border border-[#E8EDFC] shadow-sm h-[600px] flex w-full">
        {/* 左侧列表 */}
        <div className="lg:w-40 w-22 flex-shrink-0 border-r border-[#E8EDFC] overflow-y-auto bg-white">
          <div className="p-4 border-b border-[#E8EDFC] bg-[#F5F8FF]">
            <h3 className="font-medium text-[#334155] flex items-center gap-2">
              <LuBrain className="hidden lg:block w-5 h-5 text-[#6366F1]" />
              研究思维链
            </h3>
            <p className="text-sm text-[#64748B] hidden lg:block">
              {thoughts.length} 个思考阶段 {isResponding && "• 正在思考中..."}
            </p>
          </div>
          <div className="p-2 relative">
            {/* 滑动背景 */}
            {activeThoughtIndex >= 0 && (
              <div
                className="absolute left-2 right-2 bg-gradient-to-r from-[#818CF8] to-[#6366F1] rounded-lg shadow-sm transition-all duration-300 ease-out z-0"
                style={{
                  height: "72px", // 固定高度，匹配思考项
                  transform: `translateY(${activeThoughtIndex * 80}px)`, // 72px高度 + 8px间距 = 80px
                }}
              />
            )}

            {thoughts.map((thought, index) => (
              <div
                key={thought.thought_number}
                onClick={() => handleThoughtClick(thought.thought_number, index)}
                className={`relative z-10 p-3 cursor-pointer flex items-center justify-between rounded-lg transition-all duration-200 mb-2 ${activeThought === thought.thought_number
                  ? "text-white"
                  : "hover:bg-[#F5F8FF] text-[#475569]"
                  }`}
                style={{ height: "72px" }} // 固定高度
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-1 min-w-0">
                    <h4
                      className={`hidden lg:block font-medium text-sm truncate ${activeThought === thought.thought_number
                        ? "text-white"
                        : "text-[#334155]"
                        }`}
                    >
                      思考序号 #{thought.thought_number}
                    </h4>
                    <p
                      className={`text-xs mt-1 line-clamp-2 ${activeThought === thought.thought_number
                        ? "text-[#E0E7FF]"
                        : "text-[#64748B]"
                        }`}
                    >
                      {thought.content}
                    </p>
                  </div>
                </div>

                <LuChevronRight
                  className={`w-4 h-4 ${activeThought === thought.thought_number
                    ? "text-white"
                    : "text-[#94A3B8]"
                    }`}
                />
              </div>
            ))}
          </div>
        </div>

        {/* 右侧详情 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {selectedThought ? (
            <>
              {/* 阶段 tab */}
              <div className="flex border-b border-[#E8EDFC] overflow-x-auto">
                {Object.keys(selectedThought.stages).map((stage) => (
                  <button
                    key={stage}
                    className={`px-2 py-1 lg:px-4 lg:py-3  cursor-pointer text-sm font-medium border-b-2 transition-colors ${activeStage === stage
                      ? "border-[#6366F1] text-[#4F46E5] bg-[#EEF2FF]"
                      : "border-transparent text-[#64748B] hover:text-[#334155] hover:bg-[#F8FAFC]"
                      }`}
                    onClick={() => setActiveStage(stage)}
                  >
                    <div className="flex items-center gap-2 capitalize">
                      {getStageIcon(stage)}
                      {stage}
                    </div>
                  </button>
                ))}
                <div className="lg:flex-1" />
                <button
                  className="flex-shrink-0 mx-4 my-2 px-3 py-1.5 cursor-pointer text-sm rounded-md bg-[#6366F1] text-white hover:bg-[#4F46E5]"
                  onClick={handleViewReport}
                >
                  查看报告
                </button>
              </div>

              {/* 阶段内容 */}
              <div className="p-4 overflow-y-auto flex-1 bg-white">
                {renderStageContent(selectedThought, activeStage)}
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-[#94A3B8] text-sm">
              请选择左侧的思考序号查看详情
            </div>
          )}
        </div>


      </div>
      {/* 报告生成状态组件 */}
      <ReportGeneratingStatus messageId={messageId} />
    </>
  );
};

export default ResearchThoughtChain;

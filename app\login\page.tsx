'use client'

import React from 'react';
import { WebAuthForm } from '@/components/web-auth-form';
import { webApi } from '@/lib/web';
import { useRouter } from 'next/navigation';

const Login = () => {
  const router = useRouter()
  const webAuth = async (username: string, password: string) => {

    const isAuthed = await webApi(username, password);
    if (isAuthed) {
      router.push('/chat')
    }
  }

  return (
    <WebAuthForm onSubmit={webAuth} />
  );
}

export default Login;

import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}


export const hasValue = (value: any) => {
  if (value === null || value === undefined) return false
  if (typeof value === 'string') return value.trim() !== ''
  if (Array.isArray(value)) return value.length > 0
  if (typeof value === 'object') return Object.keys(value).length > 0
  return true
}

// 判断数字是否有小数点，有小数点保留2位，没有直接返回
export const formatNumber = (num: number) => {
  if (num % 1 === 0) {
    return num
  } else {
    return num.toFixed(2)
  }
}
import { fetchAuthCode, fetchGetConfig } from "@/services/api"
import { message } from "antd"

export const apiAuth = async () => {
  if (!window.h5sdk) {
    // 替换原来的alert，使用自定义确认对话框
    const shouldOpenFeishu = await showOpenFeishuConfirm();
    if (shouldOpenFeishu) {
      // 跳转到飞书App
      window.location.href = 'feishu://applink.feishu.cn';
      // 如果跳转失败，备用方案打开网页版
      setTimeout(() => {
        window.location.href = 'https://applink.feishu.cn';
      }, 500);
    }
    return false;
  }

  const res = await fetchGetConfig();
  return new Promise((resolve) => {
    window.h5sdk.ready(() => {
      tt.requestAccess({
        appID: res.data,
        scopeList: [],
        async success(res) {
          const { code } = res;
          try {
            const { data } = await fetchAuthCode(code, 'feishu');
            const access_token = data.access_token;
            const refresh_token = data.refresh_token;
            localStorage.setItem('access_token', access_token);
            localStorage.setItem('refresh_token', refresh_token);
            resolve(true);
          } catch (error) {
            message.error('获取飞书授权失败');
            resolve(false);
          }
        },
        fail(err) {
          console.log(`getAuthCode failed, err:`, JSON.stringify(err));
          resolve(false);
        }
      });
    });
  });
};

// 自定义确认对话框函数
function showOpenFeishuConfirm() {
  return new Promise((resolve) => {
    // 创建对话框容器
    const modal = document.createElement('div');
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.right = '0';
    modal.style.bottom = '0';
    modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
    modal.style.display = 'flex';
    modal.style.justifyContent = 'center';
    modal.style.alignItems = 'center';
    modal.style.zIndex = '9999';

    // 创建对话框内容
    const content = document.createElement('div');
    content.style.backgroundColor = 'white';
    content.style.padding = '20px';
    content.style.borderRadius = '8px';
    content.style.width = '300px';
    content.style.textAlign = 'center';

    // 添加提示文字
    const text = document.createElement('p');
    text.textContent = '请在飞书应用中打开此链接';
    text.style.marginBottom = '20px';
    content.appendChild(text);

    // 添加链接提示
    const linkText = document.createElement('p');
    linkText.textContent = 'https://applink.feishu.cn 想打开此应用。';
    linkText.style.fontSize = '12px';
    linkText.style.color = '#666';
    linkText.style.marginBottom = '20px';
    content.appendChild(linkText);

    // 添加复选框
    const checkboxContainer = document.createElement('div');
    checkboxContainer.style.textAlign = 'left';
    checkboxContainer.style.marginBottom = '20px';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.id = 'rememberChoice';

    const label = document.createElement('label');
    label.htmlFor = 'rememberChoice';
    label.textContent = '始终允许 applink.feishu.cn 在关联的应用中打开此类链接';
    label.style.fontSize = '12px';
    label.style.marginLeft = '5px';

    checkboxContainer.appendChild(checkbox);
    checkboxContainer.appendChild(label);
    content.appendChild(checkboxContainer);

    // 添加按钮容器
    const buttons = document.createElement('div');
    buttons.style.display = 'flex';
    buttons.style.justifyContent = 'space-between';

    // 添加取消按钮
    const cancelBtn = document.createElement('button');
    cancelBtn.textContent = '取消';
    cancelBtn.style.padding = '8px 16px';
    cancelBtn.style.border = '1px solid #ccc';
    cancelBtn.style.borderRadius = '4px';
    cancelBtn.style.backgroundColor = 'white';
    cancelBtn.style.cursor = 'pointer';
    cancelBtn.onclick = () => {
      document.body.removeChild(modal);
      resolve(false);
    };

    // 添加打开飞书按钮
    const openBtn = document.createElement('button');
    openBtn.textContent = '打开飞书';
    openBtn.style.padding = '8px 16px';
    openBtn.style.border = 'none';
    openBtn.style.borderRadius = '4px';
    openBtn.style.backgroundColor = '#3370ff';
    openBtn.style.color = 'white';
    openBtn.style.cursor = 'pointer';
    openBtn.onclick = () => {
      // 如果用户勾选了"始终允许"，存储这个选择
      if (checkbox.checked) {
        localStorage.setItem('alwaysOpenFeishu', 'true');
      }
      document.body.removeChild(modal);
      resolve(true);
    };

    buttons.appendChild(cancelBtn);
    buttons.appendChild(openBtn);
    content.appendChild(buttons);

    modal.appendChild(content);
    document.body.appendChild(modal);
  });
}
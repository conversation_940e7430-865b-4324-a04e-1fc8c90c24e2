import { FC, useState } from "react";
import {
  LuLink,
  LuFileText,
  LuChevronDown,
  LuPlay,
  LuExternalLink,
} from "react-icons/lu";
import { IoMdClose } from "react-icons/io";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FcPrevious } from "react-icons/fc";
import * as Popover from "@radix-ui/react-popover";
import { cn } from "@/lib/utils";
import { useReferenceStore } from "@/store/useReference";
import { useSidePanelStore } from "@/store/useSidePanel";
import { shallow } from "zustand/shallow";
import useIsMobile from "@/hooks/useIsMobile";

interface ChunkMetadataItem {
  type: string; // 文件类型，例如 pdf, video, audio 等
  audio_start_ms?: number; // 音频或视频片段的起始时间，单位为毫秒
  file_name: string;
  file_path: string;
  file_format: string;
  file_size?: number;
  word_count?: number;
  chunk_order: number;
  total_pages?: number;
  page?: number;
  page_label?: string;
  content_text: string;
  created_time?: string;
  modified_time?: string;
  suorce?: string; // 引用来源
  [key: string]: any; // 其他可能的字段
}

interface ChunkMetadata {
  chunkMetadata: ChunkMetadataItem[];
}

export const RagReference: FC<ChunkMetadata> = ({ chunkMetadata }) => {
  const isMobile = useIsMobile(768);
  const [currentChunk, setCurrentChunk] = useState(0);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const { addReference } = useReferenceStore(
    (state) => ({
      addReference: state.addReference,
    }),
    shallow
  );

  const { openSidePanel } = useSidePanelStore(
    (state) => ({
      openSidePanel: state.openSidePanel,
    }),
    shallow
  );

  if (chunkMetadata.length === 0) return null;

  const currentFragment = chunkMetadata[currentChunk];

  // 处理查看引用来源
  const handleViewSource = () => {
    const fragment = currentFragment;
    addReference({
      ...fragment,
      type: fragment.type,
      source: fragment.source,
      fragment: fragment.content_text,
      page: Number(fragment.page) + 1 || 1,
    });
    openSidePanel("reference");
    if (isMobile) {
      // 移动端点击查看引用后关闭Popover
      setIsPopoverOpen(false);
    }
  };

  // 获取按钮文本和图标
  const getSourceButtonInfo = () => {
    if (!currentFragment) return null;
    switch (currentFragment.type) {
      case "pdf":
        return {
          text: `查看原文档 (第${Number(currentFragment.page) + 1}页)`,
          icon: <LuFileText size={14} />,
        };
      case "video":
        const videoStartTime = currentFragment.start_ms
          ? Math.floor(currentFragment.start_ms / 1000)
          : 0;
        const minutes = Math.floor(videoStartTime / 60);
        const seconds = videoStartTime % 60;
        return {
          text: `观看视频片段 (${minutes}:${seconds
            .toString()
            .padStart(2, "0")})`,
          icon: <LuPlay size={14} />,
        };
      case "audio":
        const audioStartTime = currentFragment.start_ms
          ? Math.floor(currentFragment.start_ms / 1000)
          : 0;
        const audioMinutes = Math.floor(audioStartTime / 60);
        const audioSeconds = audioStartTime % 60;
        return {
          text: `收听音频片段 (${audioMinutes}:${audioSeconds
            .toString()
            .padStart(2, "0")})`,
          icon: <LuPlay size={14} />,
        };
      case "image":
        return {
          text: "查看原图",
          icon: <LuExternalLink size={14} />,
        };
      default:
        return {
          text: "查看引用来源",
          icon: <LuExternalLink size={14} />,
        };
    }
  };

  const sourceButtonInfo = getSourceButtonInfo();

  return (
    <div className="my-3">
      <Popover.Root open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <Popover.Trigger asChild>
          <div
            className={cn(
              "group inline-flex items-center px-3 py-2 rounded-lg cursor-pointer transition-all duration-200",
              "bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100",
              "border border-blue-200/60 hover:border-blue-300/80",
              "shadow-sm hover:shadow-md",
              "transform "
            )}
            tabIndex={0}
          >
            <div className="flex items-center space-x-2">
              <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full group-hover:bg-blue-200 transition-colors">
                <LuFileText size={16} className="text-blue-600" />
              </div>
              <div className="flex flex-col">
                <div className="flex items-center space-x-1">
                  <span className="text-sm font-medium text-blue-700 group-hover:text-blue-800">
                    查看引用内容
                  </span>
                  {/* <span className="text-gray-400">/</span> */}
                </div>
                <span className="text-xs text-gray-500 group-hover:text-gray-600">
                  共 {chunkMetadata.length} 个片段
                </span>
              </div>
            </div>
            <LuChevronDown
              size={16}
              className="ml-2 text-gray-400 group-hover:text-gray-600 transition-transform group-hover:rotate-180"
            />
          </div>
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
            side="bottom"
            align="start"
            className={cn(
              "z-50 mt-3 p-4 bg-white rounded-xl shadow-xl border border-gray-100",
              "w-96 max-w-[90vw]",
              "animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95"
            )}
            onOpenAutoFocus={(e) => e.preventDefault()}
          >
            {/* 头部标题 */}
            <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-100">
              <div className="flex items-center space-x-2">
                <LuFileText size={16} className="text-blue-600" />
                <span className="text-sm font-medium text-gray-800">
                  引用内容
                </span>
              </div>
              <Popover.Close
                className="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-1 transition-colors"
                aria-label="关闭"
              >
                <IoMdClose size={16} />
              </Popover.Close>
            </div>

            {/* 内容区域 */}
            <div
              className={cn(
                "text-sm text-gray-700 whitespace-pre-line leading-relaxed",
                "min-h-[80px] max-h-[200px] overflow-y-auto",
                "p-3 bg-gray-50/50 rounded-lg border border-gray-100"
              )}
            >
              {chunkMetadata[currentChunk]?.content_text || "无内容"}
            </div>

            {/* 查看引用来源按钮 */}
            {currentFragment &&
              currentFragment.source &&
              currentFragment.type && (
                <div className="mb-4">
                  <button
                    onClick={handleViewSource}
                    className={cn(
                      "inline-flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all",
                      "bg-blue-500 hover:bg-blue-600 text-white",
                      "shadow-sm hover:shadow-md transform hover:scale-[1.02]"
                    )}
                  >
                    {sourceButtonInfo.icon}
                    <span>{sourceButtonInfo.text}</span>
                  </button>
                </div>
              )}

            {/* 分页控制 */}
            <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-100">
              <button
                className={cn(
                  "flex items-center space-x-1 px-3 py-1.5 rounded-lg text-sm transition-all",
                  "hover:bg-gray-100 disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:bg-transparent",
                  "text-gray-600 hover:text-gray-800"
                )}
                disabled={currentChunk === 0}
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentChunk((p) => p - 1);
                }}
                aria-label="上一段"
              >
                <FcPrevious size={18} />
                <span>上一段</span>
              </button>

              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-800">
                  {currentChunk + 1}
                </span>
                <span className="text-xs text-gray-400">/</span>
                <span className="text-sm text-gray-500">
                  {chunkMetadata.length}
                </span>
              </div>

              <button
                className={cn(
                  "flex items-center space-x-1 px-3 py-1.5 rounded-lg text-sm transition-all",
                  "hover:bg-gray-100 disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:bg-transparent",
                  "text-gray-600 hover:text-gray-800"
                )}
                disabled={currentChunk === chunkMetadata.length - 1}
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentChunk((p) => p + 1);
                }}
                aria-label="下一段"
              >
                <span>下一段</span>
                <FcNext size={18} />
              </button>
            </div>

            {/* 底部信息 */}
            <div className="mt-3 pt-2 border-t border-gray-50">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span className="flex-1">
                  来源: {chunkMetadata[currentChunk]?.file_name || "无"}
                </span>
                <span className="w-20 line-clamp-1">
                  分段数: {chunkMetadata[currentChunk]?.chunk_order || "无"}
                </span>
              </div>
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  );
};

import React from "react";
import { Collapse, Card, Tag, Row, Col, Popover } from "antd";
import { BulbOutlined, SolutionOutlined } from "@ant-design/icons";

interface Props {
  stageData: any;
  // 从关键洞察的引用跳转到执行阶段的某个工具
  onViewExecution?: (toolIndex: number, tip?: string) => void;
}

const AnalysisStageContent: React.FC<Props> = ({
  stageData,
  onViewExecution,
}) => {
  if (!stageData)
    return <Card className="bg-[#F8FAFC] border-[#E2E8F0]">无分析数据</Card>;

  const deep = stageData.deep_analysis_result;
  if (!deep)
    return <Card className="bg-[#F8FAFC] border-[#E2E8F0]">无分析结果</Card>;

  const insights = deep.analysis_findings?.key_insights || [];
  // new_questions_and_gaps 可能在 analysis_findings 下，也可能直接在 deep 下
  const gaps =
    (deep.analysis_findings?.new_questions_and_gaps ??
      deep.new_questions_and_gaps) ||
    [];
  const recommendations = deep.recommendations || [];
  const status = deep.task_status_assessment || {};
  // 成功标准评估可能位于 analysis_findings 下，或直接在 deep 下
  const success =
    (deep.analysis_findings?.success_criteria_assessment ??
      deep.success_criteria_assessment) ||
    {};

  /* ---------- 颜色工具函数 ---------- */
  const confidenceColor = (c: string) =>
    ({ 高: "#059669", 中: "#D97706", 低: "#6B7280" }[c] || "#6B7280");
  const priorityColor = (p: string) =>
    ({ 高: "#DC2626", 中: "#D97706", 低: "#6B7280" }[p] || "#6B7280");
  const goalColor = (g: string) =>
    ({ 完全达成: "#059669", 基本达成: "#D97706", 未达成: "#DC2626" }[g] ||
    "#6B7280");

  /* ---------- 新写法：items 数组 ---------- */
  const items = [
    {
      key: "insights",
      label: (
        <span className="flex items-center gap-2">
          <BulbOutlined className="text-[#F59E0B]" />
          关键洞察 ({insights.length})
        </span>
      ),
      children: (
        <Row gutter={[16, 16]}>
          {insights.map((ins: any, idx: number) => (
            <Col xs={24} md={12} key={idx}>
              <Card
                size="small"
                hoverable
                className="border-[#E2E8F0] shadow-sm"
              >
                <div className="text-sm mb-2 text-[#334155]">{ins.insight}</div>
                {ins.confidence && (
                  <Tag color={confidenceColor(ins.confidence)}>
                    {ins.confidence}置信
                  </Tag>
                )}
                {[
                  ...(ins.supporting_data || []),
                  ...(ins.supporting_evidence || []),
                ].length > 0 && (
                  <ul className="list-disc pl-4 mt-2 text-xs text-[#475569]">
                    {[...ins.supporting_data, ...ins.supporting_evidence].map(
                      (d: string, i: number) => (
                        <li key={i}>{d}</li>
                      )
                    )}
                  </ul>
                )}

                {/* 引用气泡：来自 supporting_sources，带 tool_index 可跳转到执行结果 */}
                {Array.isArray(ins.supporting_sources) &&
                  ins.supporting_sources.length > 0 && (
                    <div className="mt-3 flex items-center flex-wrap gap-2">
                      <span className="text-xs text-[#64748B]">证据来源:</span>
                      {ins.supporting_sources.map((src: any, i: number) => {
                        const tIndex = src?.tool_index;
                        const num = typeof tIndex === "number" ? tIndex : i + 1;
                        const tip =
                          typeof src?.reference === "string"
                            ? src.reference
                            : JSON.stringify(src?.reference || src);
                        const clickable =
                          typeof tIndex === "number" && onViewExecution;
                        return (
                          <Popover
                            key={i}
                            styles={{ body: { maxWidth: 360 } }}
                            content={
                              <div className="max-w-[360px] text-xs leading-5">
                                <div className="text-[#334155] mb-2">{tip}</div>
                                {clickable && (
                                  <div>
                                    <a
                                      className="text-[#6366F1] hover:text-[#4F46E5]"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        onViewExecution?.(tIndex, tip);
                                      }}
                                    >
                                      查看对应执行结果
                                    </a>
                                  </div>
                                )}
                              </div>
                            }
                          >
                            <button
                              className={`w-5 h-5 inline-flex items-center justify-center rounded-full text-[11px] border ${
                                clickable
                                  ? "bg-[#EEF2FF] text-[#4F46E5] border-[#C7D2FE] hover:bg-[#E0E7FF]"
                                  : "bg-[#F1F5F9] text-[#64748B] border-[#E2E8F0]"
                              }`}
                              onClick={() => clickable && onViewExecution?.(tIndex, tip)}
                              aria-label={`引用 ${num}`}
                            >
                              {num}
                            </button>
                          </Popover>
                        );
                      })}
                    </div>
                  )}
              </Card>
            </Col>
          ))}
        </Row>
      ),
    },
    {
      key: "recommendations",
      label: (
        <span className="flex items-center gap-2">
          <SolutionOutlined className="text-[#6366F1]" />
          建议措施 ({recommendations.length})
        </span>
      ),
      children: (
        <Row gutter={[16, 16]}>
          {recommendations.map((rec: any, idx: number) => (
            <Col xs={24} md={12} key={idx}>
              <Card
                size="small"
                hoverable
                className="border-[#E2E8F0] shadow-sm"
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-sm text-[#334155]">
                    {rec.type || "建议"}
                  </span>
                  {rec.priority && (
                    <Tag color={priorityColor(rec.priority)}>
                      优先级：{rec.priority}
                    </Tag>
                  )}
                </div>
                <p className="text-sm mb-2 text-[#334155]">{rec.action}</p>
                {rec.expected_impact && (
                  <div className="text-xs text-[#64748B]">
                    预期：{rec.expected_impact}
                  </div>
                )}
                {rec.timeline && (
                  <div className="text-xs text-[#64748B]">
                    时间：{rec.timeline}
                  </div>
                )}
              </Card>
            </Col>
          ))}
        </Row>
      ),
    },
    {
      key: "status",
      label: (
        <span className="flex items-center gap-2">
          <SolutionOutlined className="text-[#059669]" />
          任务状态评估
        </span>
      ),
      children: (
        <div className="space-y-12">
          {/* 任务达成度与下一步建议 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Card
                size="small"
                className="border-[#E2E8F0] shadow-sm"
                title="总体目标达成情况"
              >
                <div className="text-sm">
                  {status.overall_goal_achieved ? (
                    <Tag color={goalColor(status.overall_goal_achieved)}>
                      {status.overall_goal_achieved}
                    </Tag>
                  ) : (
                    <span className="text-[#64748B]">无数据</span>
                  )}
                </div>
              </Card>
            </Col>
            <Col xs={24} md={12}>
              <Card
                size="small"
                className="border-[#E2E8F0] shadow-sm"
                title="下一步建议"
              >
                <div className="text-sm text-[#334155]">
                  {status.next_step_recommendation || "无"}
                </div>
              </Card>
            </Col>
          </Row>

          {/* 成功标准评估 */}
          <Card
            size="small"
            className="border-[#E2E8F0] shadow-sm"
            title="成功标准评估"
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <Card
                  size="small"
                  className="h-full border-[#E2E8F0] shadow-sm"
                  title="分析深度"
                >
                  <div className="text-sm text-[#334155]">
                    {success.analysis_depth || "无"}
                  </div>
                </Card>
              </Col>
              <Col xs={24} md={8}>
                <Card
                  size="small"
                  className="h-full border-[#E2E8F0] shadow-sm"
                  title="决策支持"
                >
                  <div className="text-sm text-[#334155]">
                    {success.decision_support || "无"}
                  </div>
                </Card>
              </Col>
              <Col xs={24} md={8}>
                <Card
                  size="small"
                  className="h-full border-[#E2E8F0] shadow-sm"
                  title="数据完整性"
                >
                  <div className="text-sm text-[#334155]">
                    {success.data_completeness || "无"}
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
        </div>
      ),
    },

    {
      key: "next",
      label: (
        <span className="flex items-center gap-2">
          <SolutionOutlined className="text-[#D97706]" />
          下一步行动 ({gaps.length})
        </span>
      ),
      children: (
        <Card size="small" className="border-[#E2E8F0] shadow-sm">
          {gaps && gaps.length > 0 ? (
            <ul className="list-disc pl-5 space-y-2 text-sm text-[#334155]">
              {gaps.map((g: string, i: number) => (
                <li key={i}>{g}</li>
              ))}
            </ul>
          ) : (
            <div className="text-sm text-[#64748B]">暂无下一步行动</div>
          )}
        </Card>
      ),
    },
  ];

  return (
    <div className="pr-2">
      <Collapse
        bordered={false}
        defaultActiveKey={["insights", "status", "recommendations", "next"]}
        className="bg-white"
        items={items} /* ← 新 API */
      />
    </div>
  );
};

export default AnalysisStageContent;

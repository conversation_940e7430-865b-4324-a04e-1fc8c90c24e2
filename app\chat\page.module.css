.container {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.mainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease, margin-right 0.3s ease;
  width: 100%;
}

.sidebarVisible .mainContent {
  width: calc(100% - var(--sidebar-width));
}

.sidebar {
  height: 100%;
  position: relative;
  transition: width 0.3s ease;
  overflow: hidden;
  width: 0;
}

.sidebarVisible .sidebar {
  width: var(--sidebar-width);
}

/* 针对不同类型侧边栏的宽度调整 */
.container[data-panel-type="reference"] {
  --sidebar-width: 650px;
}

.container[data-panel-type="web-search"] {
  --sidebar-width: 380px;
}

.container[data-panel-type="report"] {
  --sidebar-width: 800px;
}

/* 响应式设计 */
@media (max-width: 1024px) {

  .container[data-panel-type="reference"],
  .container[data-panel-type="web-search"] {
    --sidebar-width: 320px;
  }
}

/* 移动端样式 */
@media (max-width: 768px) {
  .container {
    position: relative;
  }

  .mainContent {
    width: 100% !important;
  }

  /* 移动端不使用CSS模块的sidebar样式，改用Tailwind的绝对定位 */
  .sidebar {
    display: none;
  }
}
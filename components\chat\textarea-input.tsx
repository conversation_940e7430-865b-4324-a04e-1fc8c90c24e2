import React, { useState } from "react";
import { Textarea } from "../ui/textarea";
import { IoSendSharp } from "react-icons/io5";
import { IoIosArrowDown } from "react-icons/io";
import { FaStopCircle } from "react-icons/fa";
import { cn } from "@/lib/utils";
import { useConversionStore } from "@/store/useConversion";
import { shallow } from "zustand/shallow";
import classnames from "classnames";
import { TbBook, TbWorld, TbBrain, TbDatabase } from "react-icons/tb";
import { useTranslation } from "react-i18next";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";

interface TextareaInputProps {
  scrollToBottom: () => void;
  isBottom: boolean;
}

// 按钮配置
const buttonConfigs = [
  {
    icon: TbDatabase,
    color: "#6366F1",
    name: "dataset",
  },
  {
    icon: TbBook,
    color: "#8B5CF6",
    name: "kb",
  },
  {
    icon: TbWorld,
    color: "#10B981",
    name: "web",
  },
  {
    icon: TbBrain,
    color: "#F59E0B",
    name: "deep",
  },
];

const TextareaInput = ({ scrollToBottom, isBottom }: TextareaInputProps) => {
  const { t } = useTranslation();
  const {
    handleSendMessage,
    isResponding,
    stopChatMessage,
    chatType,
    setChatType,
  } = useConversionStore(
    (state) => ({
      handleSendMessage: state.handleSendMessage,
      isResponding: state.isResponding,
      stopChatMessage: state.stopChatMessage,
      chatType: state.chatType,
      setChatType: state.setChatType,
    }),
    shallow
  );

  const [query, setQuery] = useState("");

  // 回车发送
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const newQuery = query.replace(/\n/g, "").trim();
    if (!newQuery.length || isResponding) return;
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(query);
      clearQuery();
      scrollToBottom();
    }
  };

  const clearQuery = () => {
    setQuery("");
  };

  const handleClick = () => {
    const newQuery = query.replace(/\n/g, "").trim();
    if (!newQuery.length || isResponding) return;
    handleSendMessage(query);
    clearQuery();
    scrollToBottom();
  };

  return (
    <div className="sticky bottom-0 lg:pb-6 pb-4 px-4 left-0 min-h-[140px] max-h-[180px] w-full bg-white">
      <div
        onClick={scrollToBottom}
        className={cn(
          "absolute cursor-pointer left-[50%] -top-14 p-2 bg-white rounded-full shadow-lg isolate",
          "transition-all duration-300 ease-in-out", // 添加过渡动画
          {
            "opacity-0 invisible": isBottom, // 替换 hidden
            "opacity-100 visible": !isBottom, // 替换 block
          }
        )}
      >
        <div
          className={classnames("absolute inset-0 rounded-[1.5rem]", {
            "border-2 border-t-transparent border-r-transparent border-b-transparent border-blue-300 animate-spin":
              isResponding,
          })}
        ></div>
        <IoIosArrowDown className="text-xl text-blue-300 relative z-10" />
      </div>
      <div className="lg:w-[790px] flex border border-[#EBEBEB] flex-col max-w-full w-full mx-auto rounded-2xl bg-[#F3F4F6] h-full">
        <Textarea
          className="outline-none border-0 shadow-none p-4 text-sm h-full resize-none focus:outline-none focus-visible:ring-0 focus:border-0"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        <div className="h-10 w-full p-4 flex items-center justify-between">
          {/* PC端：水平排列（lg及以上屏幕显示） */}
          <div className="hidden lg:flex items-center space-x-2 flex-1 min-w-0 overflow-x-auto whitespace-nowrap pr-2">
            {buttonConfigs.map((config) => {
              const IconComponent = config.icon;
              const isActive = chatType === config.name;

              return (
                <button
                  key={config.name}
                  className={cn(
                    "inline-flex cursor-pointer items-center gap-2 lg:px-3 px-2 py-1.5 rounded-lg lg:text-sm text-xs font-medium transition-all duration-200 relative group w-[88px] lg:w-auto lg:min-w-[110px] lg:max-w-[170px] justify-start shrink-0",
                    isActive
                      ? "bg-blue-600 text-white shadow-md border border-blue-600"
                      : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md"
                  )}
                  onClick={() =>
                    setChatType(
                      chatType === config.name ? "default" : config.name
                    )
                  }
                >
                  {/* 图标 */}
                  <IconComponent
                    className={cn(
                      "w-3.5 h-3.5 transition-all duration-200",
                      isActive ? "text-white" : "group-hover:scale-110"
                    )}
                    style={!isActive ? { color: config.color } : {}}
                  />

                  {/* 文本 */}
                  <span
                    className={cn(
                      "transition-all duration-200 truncate whitespace-nowrap max-w-full lg:max-w-[8rem]",
                      isActive ? "text-white" : "text-gray-700"
                    )}
                  >
                    {t(`common.chatType.${config.name}`)}
                  </span>
                </button>
              );
            })}
          </div>

          {/* 移动端：下拉选择（lg以下屏幕显示） */}
          <div className="lg:hidden flex-1 min-w-0 pr-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="flex items-center gap-2 px-3 py-1.5 rounded-lg border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 shadow-sm hover:shadow-md transition-all duration-200 max-w-[150px]">
                  {(() => {
                    const currentConfig =
                      buttonConfigs.find(
                        (config) => config.name === chatType
                      ) || buttonConfigs[0];
                    const CurrentIcon = currentConfig.icon;
                    return (
                      <>
                        <CurrentIcon
                          className="w-3.5 h-3.5"
                          style={{ color: currentConfig.color }}
                        />
                        <span className="text-xs truncate">
                          {t(`common.chatType.${currentConfig.name}`)}
                        </span>
                      </>
                    );
                  })()}
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[160px]">
                {buttonConfigs.map((config) => {
                  const IconComponent = config.icon;
                  const isActive = chatType === config.name;
                  return (
                    <DropdownMenuItem
                      key={config.name}
                      onClick={() => setChatType(config.name)}
                      className={cn(
                        "flex items-center gap-2 px-3 py-2 cursor-pointer text-xs",
                        isActive && "bg-blue-100 text-blue-600"
                      )}
                    >
                      <IconComponent
                        className="w-3.5 h-3.5"
                        style={{ color: config.color }}
                      />
                      <span>{t(`common.chatType.${config.name}`)}</span>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          {isResponding ? (
            <FaStopCircle
              className="text-2xl text-blue-500 cursor-pointer"
              onClick={() => stopChatMessage()}
            />
          ) : (
            <IoSendSharp
              onClick={handleClick}
              className={cn("text-2xl text-gray-500 cursor-pointer", {
                "text-blue-500": query,
              })}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default TextareaInput;

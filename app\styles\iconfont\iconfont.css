@font-face {
  font-family: "iconfont"; /* Project id 4844044 */
  src: url('iconfont.woff2?t=1748330471109') format('woff2'),
       url('iconfont.woff?t=1748330471109') format('woff'),
       url('iconfont.ttf?t=1748330471109') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-a-003-donut:before {
  content: "\e78e";
}

.icon-a-002-burger:before {
  content: "\e78f";
}

.icon-a-011-sandwich:before {
  content: "\e790";
}

.icon-a-017-cheese:before {
  content: "\e791";
}

.icon-a-019-corndog:before {
  content: "\e792";
}

.icon-2:before {
  content: "\e611";
}

.icon-1huojian:before {
  content: "\e647";
}

.icon-remen:before {
  content: "\e6a6";
}

.icon-iconfont-:before {
  content: "\e7fc";
}

.icon-dengpao:before {
  content: "\e62b";
}

.icon-info:before {
  content: "\e61e";
}

.icon-xingxing:before {
  content: "\e75b";
}

.icon-icon-taikong8:before {
  content: "\e644";
}

.icon-xiaolian:before {
  content: "\e645";
}

.icon-H5xinjianduihua:before {
  content: "\e672";
}

.icon-wangluo:before {
  content: "\e62e";
}

.icon-tushu:before {
  content: "\e6b9";
}


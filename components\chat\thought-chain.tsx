import React, { useState, useEffect, useRef } from 'react';
import { LuChevronDown, LuChevronUp, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LuFileText } from 'react-icons/lu';
import { useSidePanelStore } from '@/store/useSidePanel';
import { useReportStore } from '@/store/useReport';
import { useConversionStore } from '@/store/useConversion';

interface ThoughtStep {
  id: string;
  title: string;
  content: string;
  status: 'pending' | 'thinking' | 'completed';
  duration?: string;
  learnings: string;
  details?: {
    analysis?: string[];
    data?: string[];
    conclusion?: string;
    tools?: string[];
    needQuerys?: string[];
    results?: any[];
    references?: {
      databases?: any[];
      webPages?: any[];
      knowledgeBase?: any[];
    };
  };
}

interface ThoughtChainProps {
  steps: ThoughtStep[];
  isResponding?: boolean;
  finalReport?: string; // 新增：最终报告HTML内容
  rawThoughts?: any[]; // 新增：原始思考数据，用于判断 next_thought_needed
  hasReport?: boolean;
  messageId: string;
}

const ThoughtChain: React.FC<ThoughtChainProps> = ({ steps, isResponding = false, finalReport, rawThoughts, hasReport = false, messageId }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeStep, setActiveStep] = useState<string | null>(null);
  const [activeStepIndex, setActiveStepIndex] = useState<number>(-1); // 激活步骤的索引，用于滑动效果
  const { isCollapsed, changeCollapsed } = useConversionStore((state) => ({
    isCollapsed: state.isCollapsed,
    changeCollapsed: state.changeCollapsed
  }))
  // 侧边栏和报告状态管理
  const { openSidePanel } = useSidePanelStore();
  const { setReportForMessage, setCurrentReport } = useReportStore();

  // 为右侧详情区域容器创建 ref
  const contentContainerRef = useRef<HTMLDivElement>(null);

  // 检查是否有最终报告
  // const hasReport = hasReport;

  // 自动激活最新的步骤
  useEffect(() => {
    if (steps && steps.length > 0) {
      const latestStep = steps[steps.length - 1];
      const latestIndex = steps.length - 1;
      if (latestStep && latestStep.id !== activeStep) {
        setActiveStep(latestStep.id);
        setActiveStepIndex(latestIndex);
        // 自动滚动到最新步骤
        setTimeout(() => {
          scrollToStep(latestStep.id);
        }, 100); // 延迟一点确保DOM已更新
      }
    }
  }, [steps]);

  // 如果没有思考内容，不渲染组件
  if (!steps || steps.length === 0) {
    return null;
  }

  const hasContent = steps.length > 0 || isResponding;

  // 初始化时设置最后步骤为激活状态
  useEffect(() => {
    if (steps.length > 0 && !activeStep) {
      setActiveStep(steps[steps.length - 1].id);
    }
  }, [steps, activeStep]);

  // 滚动到指定步骤
  const scrollToStep = (stepId: string) => {
    const element = document.getElementById(`step-detail-${stepId}`);
    // 使用 ref 获取当前组件实例的容器
    const container = contentContainerRef.current;

    if (element && container) {
      // 计算元素相对于容器的位置
      const elementRect = element.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      const scrollTop = container.scrollTop;
      // 计算需要滚动的距离
      const targetScrollTop = scrollTop + (elementRect.top - containerRect.top) - 20; // 20px 的顶部间距
      // 平滑滚动到目标位置
      container.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
    }
  };

  // 手动点击步骤时的处理
  const handleStepClick = (stepId: string, index: number) => {
    setActiveStep(stepId);
    setActiveStepIndex(index);
    scrollToStep(stepId);
  };

  // 打开报告侧边栏
  const handleOpenReport = () => {
    if (hasReport) {
      setReportForMessage(messageId, {
        title: "分析报告",
        htmlContent: finalReport,
        msgId: messageId,
      });
      // 同步设置当前报告，供 ReportPanel 以 currentReport.msgId 作为 targetMessageId 正确选择显示
      setCurrentReport({
        title: "分析报告",
        htmlContent: finalReport,
        msgId: messageId,
      });
      openSidePanel('report');
      if (!isCollapsed) {
        changeCollapsed()
      }
    }
  };


  return (
    <div className="mb-4">
      {/* 收起状态 */}
      {!isExpanded && hasContent && (
        <div
          className="flex items-center cursor-pointer text-[#6076F5] text-sm hover:text-[#4A5FC0] transition-colors"
          onClick={() => setIsExpanded(true)}
        >
          <span className={`relative ${isResponding ? 'shimmer-text' : ''}`}>
            深度搜索
          </span>
          <LuChevronDown size={16} className="ml-1" />
        </div>
      )}

      {/* 展开状态 */}
      {isExpanded && (
        <div className="w-full">
          {/* 标题栏 */}
          <div className="flex items-center justify-between mb-4">
            <div
              className="flex items-center cursor-pointer text-[#6076F5] text-sm hover:text-[#4A5FC0] transition-colors"
              onClick={() => setIsExpanded(false)}
            >
              <span>深度搜索</span>
              <LuChevronUp size={16} className="ml-1" />
            </div>

          </div>

          {/* 主要内容区域 - 左右分栏 */}
          <div className="flex bg-white rounded-lg border border-gray-200 overflow-hidden max-h-[600px]">
            {/* 左侧步骤列表 */}
            <div className="w-[30%] bg-gray-50 border-r border-gray-200 p-4">
              <div className="text-sm text-gray-600 mb-4 font-medium">
                深度搜索
                {/* <div className="text-xs text-gray-400 font-normal">1m 18s</div> */}
              </div>

              <div className="space-y-2 relative">
                {/* 滑动背景 */}
                {activeStepIndex >= 0 && (
                  <div
                    className="absolute left-0 w-full bg-[#6076F5] rounded-lg shadow-sm transition-all duration-300 ease-out z-0"
                    style={{
                      height: '48px', // 固定高度，匹配步骤项
                      transform: `translateY(${activeStepIndex * 56}px)` // 48px高度 + 8px间距 = 56px
                    }}
                  />
                )}

                {steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`relative z-10 flex items-center space-x-3 px-3 rounded-lg cursor-pointer transition-all duration-200 ${activeStep === step.id
                      ? 'text-white'
                      : 'hover:bg-gray-100 text-gray-700'
                      }`}
                    style={{ height: '48px' }} // 固定高度
                    onClick={() => handleStepClick(step.id, index)}
                  >
                    {/* 状态图标 */}
                    <div className="flex-shrink-0">
                      {step.status === 'thinking' && (
                        <LuLoader className={`w-4 h-4 animate-spin ${activeStep === step.id ? 'text-white' : 'text-[#6076F5]'}`} />
                      )}
                      {step.status === 'completed' && (
                        <LuCheck className={`w-4 h-4 ${activeStep === step.id ? 'text-white' : 'text-[#27AE60]'}`} />
                      )}
                      {step.status === 'pending' && (
                        <div className={`w-4 h-4 rounded-full border-2 ${activeStep === step.id ? 'border-white' : 'border-gray-300'}`}></div>
                      )}
                    </div>

                    {/* 步骤信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">
                        {step.title}
                      </div>
                      {step.duration && (
                        <div className={`text-xs ${activeStep === step.id ? 'text-blue-100' : 'text-gray-400'}`}>
                          {step.duration}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {/* 思考中状态 - 当最后一个步骤需要下一步思考时显示 */}
                {(() => {
                  // 检查最后一个原始思考数据是否需要下一步思考
                  const lastRawThought = rawThoughts && rawThoughts.length > 0 ? rawThoughts[rawThoughts.length - 1] : null;
                  const needsNextThought = lastRawThought?.next_thought_needed === true;

                  return isResponding && steps.length > 0 && needsNextThought && (
                    <div className="flex items-center space-x-3 p-3 rounded-lg text-gray-700">
                      <div className="flex-shrink-0">
                        <LuLoader className="w-4 h-4 animate-spin text-[#6076F5]" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium">
                          思考中...
                        </div>
                        <div className="text-xs text-gray-400">
                          正在分析处理
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </div>

              {/* 查看报告按钮 */}
              {hasReport && (
                <button
                  onClick={handleOpenReport}
                  className="flex cursor-pointer mt-4 items-center space-x-2 px-3 py-1.5 bg-[#6076F5] text-white text-sm rounded-md hover:bg-[#4A5FC0] transition-colors"
                >
                  <LuFileText size={14} />
                  <span>查看报告</span>
                </button>
              )}
            </div>

            {/* 右侧详情区域 - 连贯的完整内容 */}
            <div
              ref={contentContainerRef}
              className="flex-1 overflow-y-auto no-scrollbar"
            >
              <div className="p-6 space-y-8">
                {steps.map((step) => (
                  <div key={step.id} id={`step-detail-${step.id}`} className="scroll-mt-4">
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="flex-shrink-0">
                        {step.status === 'thinking' && (
                          <LuLoader className="w-5 h-5 animate-spin text-[#6076F5]" />
                        )}
                        {step.status === 'completed' && (
                          <LuCheck className="w-5 h-5 text-[#27AE60]" />
                        )}
                        {step.status === 'pending' && (
                          <div className="w-5 h-5 rounded-full border-2 border-gray-300"></div>
                        )}
                      </div>
                      <h3 className="text-lg font-semibold text-gray-800">
                        {step.title}
                      </h3>
                      {step.duration && (
                        <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                          {step.duration}
                        </span>
                      )}
                    </div>

                    <div className="prose prose-sm max-w-none ml-7">
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {step.content}
                      </p>

                      {/* {step.details?.analysis && (
                        <div className="mb-6">
                          <h4 className="font-medium text-gray-800 mb-3 text-base">分析要点</h4>
                          <ul className="space-y-2">
                            {step.details.analysis.map((item, index) => (
                              <li key={index} className="text-gray-600 text-sm flex items-start">
                                <span className="w-1.5 h-1.5 bg-[#6076F5] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                {item}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )} */}

                      {/* {step.details?.tools && step.details.tools.length > 0 && (
                        <div className="mb-6">
                          <h4 className="font-medium text-gray-800 mb-3 text-base">使用工具</h4>
                          <div className="flex flex-wrap gap-2">
                            {step.details.tools.map((tool, index) => (
                              <span key={index} className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-xs font-medium">
                                {tool}
                              </span>
                            ))}
                          </div>
                        </div>
                      )} */}

                      {step.details?.needQuerys && step.details.needQuerys.length > 0 && (
                        <div className="mb-6">
                          <h4 className="font-medium text-gray-800 mb-3 text-base">研究方向</h4>
                          <div className="flex flex-wrap gap-2">
                            {step.details.needQuerys.map((query, index) => (
                              <div key={index} className="bg-blue-50 rounded-full overflow-hidden" style={{ maxWidth: '220px' }}>
                                <span
                                  className="inline-block w-full px-3 py-1 text-xs font-medium text-blue-700 whitespace-nowrap overflow-hidden text-ellipsis align-bottom"
                                  title={query}
                                >
                                  {query}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {step.details?.data && step.details.data.length > 0 && (
                        <div className="mb-6">
                          <h4 className="font-medium text-gray-800 mb-3 text-base">获取数据</h4>
                          <div className="bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto">
                            <ul className="space-y-2">
                              {step.details.data.map((item, index) => (
                                <li key={index} className="text-gray-600 text-sm flex items-start">
                                  <span className="w-1.5 h-1.5 bg-[#27AE60] rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                  <span className="font-mono text-xs">{item}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      )}

                      {step.details?.conclusion && (
                        <div className="bg-[#F8FAFF] border border-[#E1E8FF] p-4 rounded-lg mb-6">
                          <h4 className="font-medium text-[#6076F5] mb-2 flex items-center">
                            <span className="w-2 h-2 bg-[#6076F5] rounded-full mr-2"></span>
                            结论
                          </h4>
                          <p className="text-[#4A5FC0] text-sm leading-relaxed">
                            {step.details.conclusion}
                          </p>
                        </div>
                      )}

                      {/* 引用数据展示 */}
                      {step.details?.references && (
                        <div className="space-y-4">
                          {/* 数据库查询结果 */}
                          {step.details.references.databases && step.details.references.databases.length > 0 && (
                            <div className="border border-gray-200 rounded-lg p-4">
                              <h4 className="font-medium text-gray-800 mb-3 flex items-center">
                                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                数据库查询结果 ({step.details.references.databases.length}个查询)
                              </h4>
                              <div className="max-h-96 overflow-y-auto space-y-4">
                                {step.details.references.databases.map((queryResult: any, queryIndex: number) => (
                                  <div key={queryIndex} className="border border-gray-100 rounded-lg p-3">
                                    {/* 查询问题 */}
                                    <div className="mb-3 pb-2 border-b border-gray-100">
                                      <h5 className="font-medium text-gray-700 text-sm">查询: {queryResult.query}</h5>
                                    </div>
                                    {/* 查询结果 */}
                                    <div className="grid gap-2">
                                      {queryResult.results && queryResult.results.length > 0 ? (
                                        queryResult.results.map((item: any, index: number) => (
                                          <div key={index} className="bg-gray-50 p-3 rounded text-xs font-mono">
                                            {Object.entries(item).map(([key, value]) => (
                                              <div key={key} className="flex">
                                                <span className="text-gray-500 w-32 flex-shrink-0">{key}:</span>
                                                <span className="text-gray-700">{String(value)}</span>
                                              </div>
                                            ))}
                                          </div>
                                        ))
                                      ) : (
                                        <div className="text-gray-500 text-xs italic">暂无数据</div>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* 网页搜索结果 */}
                          {step.details.references.webPages && step.details.references.webPages.length > 0 && (
                            <div className="border border-gray-200 rounded-lg p-4">
                              <h4 className="font-medium text-gray-800 mb-3 flex items-center">
                                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                网页搜索结果 ({step.details.references.webPages.length}个查询)
                              </h4>
                              <div className="max-h-96 overflow-y-auto space-y-4">
                                {step.details.references.webPages.map((queryResult: any, queryIndex: number) => (
                                  <div key={queryIndex} className="border border-gray-100 rounded-lg p-3">
                                    {/* 查询问题 */}
                                    <div className="mb-3 pb-2 border-b border-gray-100">
                                      <h5 className="font-medium text-gray-700 text-sm">查询: {queryResult.query}</h5>
                                    </div>
                                    {/* 搜索结果 */}
                                    <div className="space-y-3">
                                      {queryResult.results && queryResult.results.length > 0 ? (
                                        queryResult.results.map((item: any, index: number) => (
                                          <div key={index} className="bg-blue-50 p-3 rounded-lg">
                                            <div className="flex items-start space-x-3">
                                              {item.favicon && (
                                                <img src={item.favicon} alt="" className="w-4 h-4 mt-1 flex-shrink-0" />
                                              )}
                                              <div className="flex-1 min-w-0">
                                                <h5 className="font-medium text-blue-900 text-sm truncate">
                                                  {item.title || '未知标题'}
                                                </h5>
                                                <p className="text-blue-700 text-xs mt-1 break-all">
                                                  {item.url}
                                                </p>
                                                {item.context && (
                                                  <p className="text-blue-600 text-xs mt-2 line-clamp-3">
                                                    {item.context.substring(0, 200)}...
                                                  </p>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        ))
                                      ) : (
                                        <div className="text-gray-500 text-xs italic">暂无搜索结果</div>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* 知识库查询结果 */}
                          {step.details.references.knowledgeBase && step.details.references.knowledgeBase.length > 0 && (
                            <div className="border border-gray-200 rounded-lg p-4">
                              <h4 className="font-medium text-gray-800 mb-3 flex items-center">
                                <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                                知识库查询结果 ({step.details.references.knowledgeBase.length}个查询)
                              </h4>
                              <div className="max-h-96 overflow-y-auto space-y-4">
                                {step.details.references.knowledgeBase.map((queryResult: any, queryIndex: number) => (
                                  <div key={queryIndex} className="border border-gray-100 rounded-lg p-3">
                                    {/* 查询问题 */}
                                    <div className="mb-3 pb-2 border-b border-gray-100">
                                      <h5 className="font-medium text-gray-700 text-sm">查询: {queryResult.query}</h5>
                                    </div>
                                    {/* 文档片段结果 */}
                                    <div className="space-y-3">
                                      {queryResult.results && queryResult.results.length > 0 ? (
                                        queryResult.results.map((item: any, index: number) => (
                                          <div key={index} className="bg-purple-50 p-3 rounded-lg">
                                            <div className="flex items-start justify-between mb-2">
                                              <h5 className="font-medium text-purple-900 text-sm">
                                                {item.file_name || '未知文件'}
                                              </h5>
                                              <div className="flex space-x-2 text-xs text-purple-600">
                                                <span>{item.word_count || 0}字</span>
                                                <span>#{item.chunk_order || 0}</span>
                                              </div>
                                            </div>
                                            {item.file_path && (
                                              <a
                                                href={item.file_path}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-purple-700 text-xs hover:underline block mb-2"
                                              >
                                                查看原文件
                                              </a>
                                            )}
                                            {item.content_text && (
                                              <div className="bg-white p-2 rounded text-xs text-gray-700 max-h-32 overflow-y-auto">
                                                {item.content_text.substring(0, 300)}
                                                {item.content_text.length > 300 && '...'}
                                              </div>
                                            )}
                                            <div className="text-xs text-purple-500 mt-2">
                                              创建: {item.created_time ? new Date(item.created_time).toLocaleDateString() : '未知'}
                                            </div>
                                          </div>
                                        ))
                                      ) : (
                                        <div className="text-gray-500 text-xs italic">暂无文档片段</div>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* 总结 */}
                      {step?.learnings && (
                        <div className="mt-4">
                          <h4 className="font-medium text-gray-800 mb-3 text-base">分析与发现</h4>
                          <p className="text-gray-600 mb-4 leading-relaxed">
                            {step?.learnings}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* 分隔线，除了最后一个步骤 */}
                    {step.id !== steps[steps.length - 1]?.id && (
                      <div className="mt-8 border-b border-gray-100"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 闪光效果的CSS */}
      <style jsx>{`
        .shimmer-text {
          background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.8) 50%,
            transparent 100%
          );
          background-size: 200% 100%;
          animation: shimmer 2s infinite;
          background-clip: text;
          -webkit-background-clip: text;
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
      `}</style>
    </div>
  );
};

export default ThoughtChain;

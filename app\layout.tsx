import "./globals.css";
import "./styles/markdown.scss"
import './styles/iconfont/iconfont.css'
import { Kanit } from "next/font/google";
import { getLocaleOnServer } from "@/i18n/server";
import I18nServer from '@/components/i18n-server'
import VConsoleScript from '@/components/vconsole'

const notoSansRejang = Kanit({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
})

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = getLocaleOnServer();

  return (
    <html lang={locale ?? 'en'} className={notoSansRejang.className}>
      <head>
        {/* 飞书jssdk */}
        <script
          src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.26.js"
        />
        {/* 钉钉jssdk */}
        <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.25/dingtalk.open.js"></script>
        {/* {process.env.NODE_ENV === 'development' && <VConsoleScript />} */}
      </head>
      <body className="h-svh overflow-hidden relative">
        <I18nServer>{children}</I18nServer>
      </body>
    </html>
  );
}

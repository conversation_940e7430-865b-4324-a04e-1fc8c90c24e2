import './generate-table.css';
import useIsMobile from '@/hooks/useIsMobile';
import { formatNumber } from '@/lib/utils';
import { ConfigProvider, Table, Tooltip } from 'antd';
import React, { memo, useEffect, useState } from 'react';

const GenerateTable = ({ dataSource }: { dataSource: any[] }) => {
  const [columns, setColumns] = useState<any>([]);
  const [newDataSource, setNewDataSource] = useState<any>([]);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (dataSource && dataSource.length > 0) {
      const keys = Object.keys(dataSource[0]);
      const columns = keys.map(key => ({
        title: key,
        dataIndex: key,
        key,
        align: 'center',
        minWidth: 50, // 设置最小宽度
        maxWidth: 100, // 设置最大宽度
        ellipsis: true, // 超出部分省略
        render: (text: any) => {
          // 判断是否为数字
          const isNumber = !isNaN(text) && text !== '';
          // 转换内容
          const content = isNumber ? formatNumber(Number(text)) : String(text);
          const truncated = String(content).length > 35 ? `${String(content).substring(0, 20)}...` : content;

          return (
            <Tooltip
              title={<div style={{
                maxHeight: '400px',  // 最大高度
                overflowY: 'auto',   // 垂直方向可滚动
                wordBreak: 'break-all', // 文字换行
                whiteSpace: 'pre-wrap' // 保留换行和空格
              }}>{content}</div>}
              trigger={isMobile ? 'click' : 'hover'}
              styles={{
                root: {
                  maxWidth: isMobile ? '60vw' : '30vw',
                  minWidth: '100px',
                },
                body: {
                  width: 'fit-content'
                }
              }}
            >
              <div style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                cursor: 'pointer',
                width: '100%',
                padding: '0 4px',
              }}>
                {truncated}
              </div>
            </Tooltip>
          );
        }
      }));
      setColumns(columns);
      setNewDataSource(dataSource.map((item, index) => ({
        ...item,
        key: index,
      })));
    }
  }, [dataSource, isMobile]);

  return (
    <ConfigProvider
      theme={{
        token: {
          fontSize: Number(1.04 * 14),
        }
      }}
    >
      <Table
        size="middle"
        dataSource={newDataSource}
        columns={columns}
        scroll={{ x: 'max-content' }}
        style={{
          minWidth: '100%',
          backgroundColor: '#fff',
        }}
        className="custom-table"
        pagination={{
          showSizeChanger: false,
          style: {
            marginRight: 0,
            backgroundColor: '#fff',
            width: '100%',
            overflow: 'hidden'
          }
        }}
      />
    </ConfigProvider>
  );
}

export default memo(GenerateTable);
import { useSidePanelStore } from "@/store/useSidePanel";
import { useWebSearchStore } from "@/store/useWebSearch";
import { FiGlobe } from "react-icons/fi";
import { IoIosArrowForward } from "react-icons/io";
import { shallow } from "zustand/shallow";

interface WebSearchUrlsProps {
  urls: {
    title: string;
    url: string;
    favicon: string;
    context: string;
    order: number;
  }[];
}
export const WebSearchUrls = ({ urls }: WebSearchUrlsProps) => {
  if (!urls || urls.length === 0) return null;

  const { setSearchResults } = useWebSearchStore((state) => ({
    setSearchResults: state.setSearchResults
  }), shallow);

  const { openSidePanel } = useSidePanelStore((state) => ({
    openSidePanel: state.openSidePanel
  }), shallow);



  // 最多显示5个图标
  const visibleUrls = urls.slice(0, 5);
  const totalCount = urls.length;

  const handerClick = () => {
    setSearchResults(urls)
    openSidePanel('web-search')
  }

  return (
    <div className="mt-4">
      <h3 className="text-lg font-semibold mb-2">相关链接</h3>
      <div className="flex items-center w-fit cursor-pointer" onClick={handerClick}>
        <div className="flex items-center relative">
          {visibleUrls.map((item, index) => (
            <div
              key={index}
              title={item.url}
              className="block"
              style={{
                zIndex: 10 - index, // 保证堆叠顺序
                marginLeft: index > 0 ? '-12px' : '0' // 堆叠效果
              }}
            >
              <span className="inline-block w-8 h-8 rounded-full border-2 border-white bg-gray-50 overflow-hidden shadow hover:scale-110 transition-transform">
                <img
                  src={item.favicon}
                  alt=""
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    // 多级备选方案
                    try {
                      const { hostname } = new URL(item.url);
                      // 备选1: Google服务
                      target.src = `https://www.google.com/s2/favicons?domain=${hostname}&sz=32`;

                      // 如果Google也失败，设置事件处理程序尝试备选2
                      target.onerror = () => {
                        target.src = `https://api.iowen.cn/favicon/${hostname}.png`;

                        // 如果备选2也失败，使用备选3
                        target.onerror = () => {
                          target.src = `https://icon.horse/icon/${hostname}`;

                          // 如果所有方法都失败，移除图片并显示默认图标
                          target.onerror = () => {
                            target.style.display = 'none';
                            const parent = target.parentElement;
                            if (parent) {
                              const icon = document.createElement('div');
                              icon.className = "w-full h-full flex items-center justify-center bg-gray-100";
                              const svgIcon = document.createElement('div');
                              parent.appendChild(icon);
                              icon.appendChild(svgIcon);
                              // 添加默认图标
                              svgIcon.innerHTML = '<svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-gray-400" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg>';
                            }
                            target.onerror = null;
                          };
                        };
                      };
                    } catch {
                      target.style.display = 'none';
                    }
                  }}
                />
                <FiGlobe className="w-full h-full text-gray-400 p-1 absolute opacity-0" />
              </span>
            </div>
          ))}
        </div>

        {/* 篇数显示 */}
        <a
          href="#"
          onClick={(e) => e.preventDefault()}
          className="flex items-center ml-2 text-sm text-blue-500 hover:text-blue-700 transition-colors"
        >
          <span>{totalCount} 篇资料</span>
          <IoIosArrowForward className="ml-1" />
        </a>
      </div>
    </div>
  );
};
import React, { memo } from 'react';
import ReactEcharts from 'echarts-for-react';
import ErrorBoundary from '../custom/error-boundary';

const ChartView = memo(({ option }: { option: any }) => {
  return (
    <div style={{
      marginTop: '20px',
      minHeight: '420px',
      minWidth: '100%',
      overflowX: 'auto',
      position: 'relative'
    }}>
      <ErrorBoundary>
        <ReactEcharts
          option={option}
          style={{
            minWidth: '100%',
            width: `${(option?.xAxis?.data?.length * 25)}px`,
            height: '400px',
          }}
          opts={{
            renderer: 'svg'
          }}
        />
      </ErrorBoundary>
    </div>
  );
});

ChartView.displayName = 'ChartView';
export default ChartView;
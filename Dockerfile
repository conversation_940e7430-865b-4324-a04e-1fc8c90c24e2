# 使用 Node.js 官方轻量镜像
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /web

RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm
RUN npm install -g pnpm

# 复制 package.json 和 pnpm-lock.yaml（若存在）
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install

# 复制源代码
COPY . .

# 构建 Next.js 项目
RUN pnpm run build

# 仅保留必要文件
FROM node:18-alpine AS runner

WORKDIR /web

# 安装 pnpm（运行时可能需要）
RUN npm install -g pnpm

# 复制构建产物和依赖
COPY --from=builder /web/node_modules ./node_modules
COPY --from=builder /web/package.json ./package.json
COPY --from=builder /web/.next ./.next
COPY --from=builder /web/public ./public

# 设置非 root 用户
USER node

# 暴露 3000 端口
EXPOSE 3000

# 启动应用
CMD ["pnpm", "start"]

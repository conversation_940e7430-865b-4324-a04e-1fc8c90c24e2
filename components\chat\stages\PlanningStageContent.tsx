import React, { useState } from "react";
import {
  FiChevronDown,
  FiChevronUp,
  FiDatabase,
  FiSearch,
  FiBook,
} from "react-icons/fi";
import { Card, List, Typography } from "antd";
import { mapToolName } from "@/utils/mapToolName";
const { Text } = Typography;

interface Props {
  stageData: any;
}

// 工具任务结构
type ToolTask = {
  key_business_metrics?: string[];
  analysis_dimensions?: string[];
  search_topics?: string[];
  focus_areas?: string[];
  data_requirements?: object | string;
};

const SectionTitle: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <h4 className="mb-3 text-sm font-semibold text-[#334155]">{children}</h4>;

const PlanningStageContent: React.FC<Props> = ({ stageData }) => {
  if (!stageData?.research_plan)
    return <div className="text-sm text-[#64748B]">无研究计划</div>;

  const plan = stageData.research_plan;
  const [openMap, setOpenMap] = useState<Record<string, boolean>>({});

  const toggle = (key: string) => setOpenMap((p) => ({ ...p, [key]: !p[key] }));

  return (
    <div className="space-y-4">
      {/* 研究方向 */}
      {plan.reasoning && (
        <div className="rounded-xl border border-[#F59E0B]/20 bg-[#F59E0B]/5 p-4 shadow-sm">
          <SectionTitle>研究方向</SectionTitle>
          <p className="text-sm text-[#B45309]">{plan.reasoning}</p>
        </div>
      )}

      {/* 成功标准 */}
      {plan.success_criteria && (
        <div className="rounded-xl border bg-white p-4 shadow-sm">
          <SectionTitle>成功标准</SectionTitle>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card
              size="small"
              className="h-full border-[#E2E8F0] shadow-sm"
              title="分析深度"
            >
              <div className="text-sm text-[#334155]">
                {plan.success_criteria?.analysis_depth || '无'}
              </div>
            </Card>
            <Card
              size="small"
              className="h-full border-[#E2E8F0] shadow-sm"
              title="决策支持"
            >
              <div className="text-sm text-[#334155]">
                {plan.success_criteria?.decision_support || '无'}
              </div>
            </Card>
            <Card
              size="small"
              className="h-full border-[#E2E8F0] shadow-sm"
              title="数据完整性"
            >
              <div className="text-sm text-[#334155]">
                {plan.success_criteria?.data_completeness || '无'}
              </div>
            </Card>
          </div>
        </div>
      )}

      {/* 研究目标 */}
      {plan.overall_goal && (
        <div className="rounded-xl border border-[#D1FAE5] bg-[#ECFDF5] p-4 shadow-sm">
          <SectionTitle>研究目标</SectionTitle>
          <p className="text-sm text-[#047857]">{plan.overall_goal}</p>
        </div>
      )}

      {/* 工具任务 */}
      {plan.tool_specific_tasks && (
        <div>
          <SectionTitle>工具任务分解</SectionTitle>
          <div className="space-y-4">
            {Object.entries(plan.tool_specific_tasks).map(([tool, raw]) => {
              const task = raw as ToolTask;
              const key = `tool-${tool}`;
              const isOpen = openMap[key] ?? true;
              const Icon =
                tool === "nl2sql"
                  ? FiDatabase
                  : tool === "search_web"
                  ? FiSearch
                  : FiBook;

              return (
                <div
                  key={tool}
                  className="rounded-xl border border-[#E8EDFC] bg-white shadow-sm"
                >
                  {/* 工具标题栏 */}
                  <button
                    onClick={() => toggle(key)}
                    className="w-full flex items-center justify-between p-4 hover:bg-[#F8FAFC] transition"
                  >
                    <div className="flex items-center gap-2">
                      <Icon className="text-[#6366F1]" />
                      <span className="font-medium text-sm text-[#6366F1]">
                        {mapToolName(tool)}
                      </span>
                    </div>
                    {isOpen ? <FiChevronUp /> : <FiChevronDown />}
                  </button>

                  {isOpen && (
                    <div className="px-4 pb-4">
                      {/* 横向两栏 - 桌面 */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* 关键业务指标 */}
                        {task.key_business_metrics?.length ? (
                          <Card
                            size="small"
                            title="关键业务指标"
                            className="shadow-sm"
                          >
                            <List
                              size="small"
                              dataSource={task.key_business_metrics}
                              renderItem={(item) => (
                                <List.Item className="!px-0 !py-1">
                                  <Text className="text-xs">{item}</Text>
                                </List.Item>
                              )}
                            />
                          </Card>
                        ) : null}

                        {/* 分析维度 */}
                        {task.analysis_dimensions?.length ? (
                          <Card
                            size="small"
                            title="分析维度"
                            className="shadow-sm"
                          >
                            <List
                              size="small"
                              dataSource={task.analysis_dimensions}
                              renderItem={(item) => (
                                <List.Item className="!px-0 !py-1">
                                  <Text className="text-xs">{item}</Text>
                                </List.Item>
                              )}
                            />
                          </Card>
                        ) : null}

                        {/* 搜索主题 */}
                        {task.search_topics?.length ? (
                          <Card
                            size="small"
                            title="搜索主题"
                            className="shadow-sm"
                          >
                            <List
                              size="small"
                              dataSource={task.search_topics}
                              renderItem={(item) => (
                                <List.Item className="!px-0 !py-1">
                                  <Text className="text-xs">{item}</Text>
                                </List.Item>
                              )}
                            />
                          </Card>
                        ) : null}

                        {/* 关注领域 */}
                        {task.focus_areas?.length ? (
                          <Card
                            size="small"
                            title="关注领域"
                            className="shadow-sm"
                          >
                            <List
                              size="small"
                              dataSource={task.focus_areas}
                              renderItem={(item) => (
                                <List.Item className="!px-0 !py-1">
                                  <Text className="text-xs">{item}</Text>
                                </List.Item>
                              )}
                            />
                          </Card>
                        ) : null}
                      </div>

                      {/* 数据要求 - 全宽 */}
                      {task.data_requirements && (
                        <div className="mt-4">
                          <h5 className="text-xs font-medium text-gray-600 mb-2">
                            数据要求
                          </h5>
                          <div className="bg-gray-50 p-3 rounded-lg text-xs">
                            {typeof task.data_requirements === "object" ? (
                              <div className="space-y-1">
                                {Object.entries(task.data_requirements).map(
                                  ([k, v]) => (
                                    <div key={k}>
                                      <span className="font-medium">{k}:</span>{" "}
                                      {String(v)}
                                    </div>
                                  )
                                )}
                              </div>
                            ) : (
                              <p>{String(task.data_requirements)}</p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default PlanningStageContent;

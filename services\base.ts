import '@ant-design/v5-patch-for-react-19';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd'

class Request {
  private instance: AxiosInstance;
  private baseConfig = {
    baseURL: `${process.env.NEXT_PUBLIC_API_URL}/api`,
    timeout: 60000,
  };


  constructor(config?: AxiosRequestConfig) {
    this.instance = axios.create({
      ...this.baseConfig,
      ...config,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => response.data,
      async (error) => {

        const originalRequest = error.config;
        const { response } = error;
        const no_auth_code = [10006, 10007, 10008, 10009]
        const code = response?.data?.code
        if (no_auth_code.includes(code) && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              // 调用刷新token的API
              const { data: { data } } = await axios.get(`/api/auth/refresh-token/${refreshToken}`);

              const { access_token, refresh_token } = data;
              localStorage.setItem('access_token', access_token);
              localStorage.setItem('refresh_token', refresh_token);

              // 更新请求头
              originalRequest.headers['Authorization'] = `Bearer ${access_token}`;

              // 重试原始请求
              return this.instance(originalRequest);
            }
          } catch (refreshError) {
            // 刷新失败则清除token并跳转认证页
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            message.error('会话已过期，请重新登录');
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }
        if (response) {
          const { data } = response;
          switch (response.status) {
            case 401:
              // 未授权，跳转到登录页
              localStorage.removeItem('token');
              window.location.href = '/login';
              message.error(data.message || '未授权，请重新登录')
              break;
            case 403:
              message.error(data.message || '没有权限访问')
              break;
            case 404:
              message.error(data.message || '请求的资源不存在')
              break;
            default:
              message.error(data.message || '服务器错误')
          }
        } else {
          message.error('网络错误，请检查您的网络连接')
        }
        return Promise.reject(error);
      }
    );
  }

  public async get<T>(url: string, params?: any): Promise<T> {
    return this.instance.get(url, { params });
  }

  public async post<T>(url: string, data?: any): Promise<T> {
    return this.instance.post(url, data);
  }

  public async put<T>(url: string, data?: any): Promise<T> {
    return this.instance.put(url, data);
  }

  public async delete<T>(url: string): Promise<T> {
    return this.instance.delete(url);
  }
}

export const request = new Request();

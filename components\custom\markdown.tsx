'use client'

import React, { lazy, memo, useCallback, useEffect, useMemo, useState } from 'react';
import ReactMarkdown from 'react-markdown'
import 'katex/dist/katex.min.css'
import ReactEcharts from 'echarts-for-react'
import RemarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'
import Syntax<PERSON><PERSON>lighter from 'react-syntax-highlighter'
import { vs } from 'react-syntax-highlighter/dist/esm/styles/hljs'
import RehypeKatex from 'rehype-katex'
import copy from 'copy-to-clipboard'
import { RiArrowDownSLine, RiArrowUpSLine, RiFileCopyLine } from 'react-icons/ri';
import { AiOutlineCheck } from 'react-icons/ai';
import ErrorBoundary from './error-boundary';
import { CitationReference } from '../chat/citation-reference';
import { visitParents } from 'unist-util-visit-parents';
import reactStringReplace from 'react-string-replace';
import { useConversionStore } from '@/store/useConversion';

interface MarkdownProps {
  content: string;
  metadata?: any;
}

// 处理引用的正则表达式
const citationRegex = /\[(\d+)\]/g;

const capitalizationLanguageNameMap: Record<string, string> = {
  sql: 'SQL',
  javascript: 'JavaScript',
  java: 'Java',
  typescript: 'TypeScript',
  vbscript: 'VBScript',
  css: 'CSS',
  html: 'HTML',
  xml: 'XML',
  php: 'PHP',
  python: 'Python',
  yaml: 'Yaml',
  mermaid: 'Mermaid',
  markdown: 'MarkDown',
  makefile: 'MakeFile',
  echarts: 'ECharts',
  shell: 'Shell',
  powershell: 'PowerShell',
  json: 'JSON',
  latex: 'Latex',
  svg: 'SVG',
}

const getCorrectCapitalizationLanguageName = (language: string) => {
  if (!language)
    return 'Plain'

  if (language in capitalizationLanguageNameMap)
    return capitalizationLanguageNameMap[language]

  return language.charAt(0).toUpperCase() + language.substring(1)
}

function rehypeWrapReference() {
  return function transformer(tree: any) {
    visitParents(tree, 'text', (node: any, ancestors: any[]) => {
      const parent = ancestors[ancestors.length - 1];
      if (parent && parent.tagName !== 'custom-typography' && parent.tagName !== 'code') {
        node.type = 'element';
        node.tagName = 'custom-typography';
        node.properties = {};
        node.children = [{ type: 'text', value: node.value }];
      }
    });
  };
}

const CodeBlock: any = memo(({ inline, className, children, ...props }: any) => {
  const [isExpanded, setIsExpanded] = useState(true)
  const [isCopied, setIsCopied] = useState(false) // 新增复制状态
  const match = /language-(\w+)/.exec(className || '')
  const language = match?.[1]
  const languageShowName = getCorrectCapitalizationLanguageName(language || '')

  const chartData = useMemo(() => {
    if (language === 'echarts') {
      try {
        const code = String(children)
          .replace(/\n$/, '')
          .replace(/'/g, '"') // 转换单引号为双引号
          .replace(/,\s*\/\/.*$/gm, ''); // 移除行尾注释

        return JSON.parse(code);
      }
      catch (error) {
        console.error('ECharts 配置解析错误:', error);
        return {
          title: {
            text: "配置解析错误",
            subtext: "请检查JSON格式是否正确"
          }
        };
      }
    }
    return JSON.parse('{"title":{"text":"ECharts error - Wrong JSON format."}}')
  }, [language, children])


  const handleCopy = useCallback(() => {
    if (isCopied) return // 防止重复复制
    copy(String(children).replace(/\n$/, ''));
    setIsCopied(true)
  }, [children, isCopied]); // 添加依赖

  useEffect(() => {
    if (isCopied) {
      const timeout = setTimeout(() => setIsCopied(false), 2000)
      return () => clearTimeout(timeout)
    }
  }, [isCopied])

  const renderCodeContent = useCallback(() => {
    const content = String(children).replace(/\n$/, '')
    if (language === 'echarts') {
      return <div style={{ minHeight: '350px', minWidth: '100%', overflowX: 'scroll' }}>
        <ErrorBoundary>
          <ReactEcharts option={chartData} style={{ minWidth: '700px' }} />
        </ErrorBoundary>
      </div>
    }

    return (
      <SyntaxHighlighter
        {...props}
        style={vs}
        customStyle={{
          paddingLeft: 12,
          backgroundColor: '#F9FAFB',
          borderBottomRightRadius: '0.5rem',
          borderBottomLeftRadius: '0.5rem',
          marginBottom: 0,
        }}
        language={match?.[1]}
        showLineNumbers
        PreTag="div"
      >
        {content}
      </SyntaxHighlighter>
    )
  }, [match?.[1], children])


  if (inline || !match) {
    return <code {...props} className={className}>{children}</code>
  }

  return (
    <div className='relative group'>
      <div
        className='flex justify-between h-8 items-center p-1 pl-3 border-b rounded-t-lg'
        style={{
          borderColor: 'rgba(0, 0, 0, 0.05)',
          backgroundColor: '#F3F4F6',
        }}
      >
        <div className='flex items-center space-x-2 cursor-pointer' onClick={() => setIsExpanded(!isExpanded)}>
          <div className='text-[13px] text-gray-500 font-normal'>{languageShowName}</div>
          {isExpanded ? <RiArrowUpSLine /> : <RiArrowDownSLine />}
        </div>
        <div className='flex pr-2'>
          <button
            onClick={handleCopy}
            className='opacity-0 cursor-pointer group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-600'
            title="复制代码"
          >
            {isCopied ? ( // 切换图标
              <AiOutlineCheck className="w-4 h-4 text-green-500" />
            ) : (
              <RiFileCopyLine className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>
      <div
        className={`grid transition-[grid-template-rows] duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]`}
        style={{
          gridTemplateRows: isExpanded ? '1fr' : '0fr',
        }}
      >
        <div className="overflow-hidden min-h-0">
          {renderCodeContent()}
        </div>
      </div>
    </div>
  )
})
CodeBlock.displayName = 'CodeBlock'

// 创建文本组件用于处理引用
const TextRenderer = ({ children, metadata }: { children: any, metadata: any }) => {

  if (!children) return null;
  if (typeof children !== 'string') return <>{children}</>;
  if (!metadata?.web_metadata || !metadata.web_metadata.length) return <>{children}</>;

  // 使用 reactStringReplace 替换引用
  const replacedContent = reactStringReplace(children, /\[(\d+)\]/g, (match, i) => {
    const index = parseInt(match, 10);
    const citationData = metadata.web_metadata.find((item: any) => item.order === index);


    if (citationData) {
      return (
        <CitationReference
          key={`citation-${index}-${i}`}
          index={index}
          metadata={citationData}
        />
      );
    }

    return `[${match}]`;
  });

  return <>{replacedContent}</>;
};

// 缓存插件实例
const remarkPlugins = [RemarkMath, RemarkGfm, RemarkBreaks];
const rehypePlugins = [RehypeKatex, rehypeWrapReference];

const Markdown = memo(({ content, metadata }: MarkdownProps) => {

  // 预处理内容
  const processedContent = useMemo(() => {
    if (!content) return '';
    return content;
  }, [content]);

  return (
    <div className='markdown-body'>
      <ReactMarkdown
        remarkPlugins={remarkPlugins}
        rehypePlugins={rehypePlugins}
        components={{
          code: CodeBlock,
          // 始终使用 TextRenderer 来处理文本内容
          'custom-typography': ({ children }: { children: React.ReactNode }) =>
            <TextRenderer children={children} metadata={metadata} />
        } as unknown as React.ComponentProps<typeof ReactMarkdown>['components']}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
}, (prev, next) => {
  // 添加更精确的比较，当内容或元数据变化时都需要重新渲染
  const prevHasMetadata = prev.metadata?.web_metadata?.length > 0;
  const nextHasMetadata = next.metadata?.web_metadata?.length > 0;

  // 如果内容相同但元数据从无到有，则需要重新渲染
  if (prev.content === next.content && !prevHasMetadata && nextHasMetadata) {
    return false;
  }

  return prev.content === next.content &&
    JSON.stringify(prev.metadata?.web_metadata) === JSON.stringify(next.metadata?.web_metadata);
});

export default Markdown;
/**
 * 报告生成状态组件
 * 显示在思维链组件下方，告知用户报告正在生成中
 */

import React from "react";
import { LuLoader, LuFileText } from "react-icons/lu";
import { useReportStore } from "@/store/useReport";

interface ReportGeneratingStatusProps {
  messageId: string;
}

const ReportGeneratingStatus: React.FC<ReportGeneratingStatusProps> = ({
  messageId,
}) => {
  const isGenerating = useReportStore((state) => state.isReportGenerating(messageId));

  if (!isGenerating) {
    return null;
  }

  return (
    <div className="mt-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 shadow-sm animate-fadeIn">
      <div className="flex items-center space-x-3">
        {/* 旋转的加载图标 */}
        <div className="flex-shrink-0">
          <LuLoader className="w-5 h-5 text-blue-600 animate-spin" />
        </div>

        {/* 文字内容 */}
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <LuFileText className="w-4 h-4 text-blue-600" />
            <h4 className="text-sm font-medium text-blue-900">
              正在生成分析报告
            </h4>
          </div>
          <p className="text-xs text-blue-700">
            系统正在基于研究结果生成详细的分析报告，请稍候...
          </p>
        </div>

        {/* 进度动画 */}
        <div className="flex-shrink-0">
          <div className="flex space-x-1">
            <div
              className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
              style={{ animationDelay: '0s' }}
            ></div>
            <div
              className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
              style={{ animationDelay: '0.1s' }}
            ></div>
            <div
              className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
              style={{ animationDelay: '0.2s' }}
            ></div>
          </div>
        </div>
      </div>

      {/* 进度条 */}
      <div className="mt-3">
        <div className="w-full bg-blue-100 rounded-full h-1.5 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-400 to-blue-600 h-1.5 rounded-full animate-pulse relative">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-shimmer"></div>
          </div>
        </div>
      </div>

      {/* 提示文字 */}
      <div className="mt-2 text-xs text-blue-600 flex items-center space-x-1">
        <span>💡</span>
        <span>报告生成完成后会自动打开，您可以继续其他操作</span>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }

        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }

        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
      `}</style>
    </div>
  );
};

export default ReportGeneratingStatus;

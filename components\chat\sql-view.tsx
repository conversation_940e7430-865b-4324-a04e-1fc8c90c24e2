import React, { memo, useState, useCallback, useEffect } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { RiFileCopyLine } from 'react-icons/ri';
import { AiOutlineCheck } from 'react-icons/ai';
import copy from 'copy-to-clipboard';

const SQLView = memo(({ sql }: { sql: string }) => {
  const [isCopied, setIsCopied] = useState(false);

  const handleCopy = useCallback(() => {
    if (isCopied) return; // 防止重复复制
    copy(String(sql).replace(/\n$/, ''));
    setIsCopied(true);
  }, [sql, isCopied]);

  useEffect(() => {
    if (isCopied) {
      const timeout = setTimeout(() => setIsCopied(false), 2000);
      return () => clearTimeout(timeout);
    }
  }, [isCopied]);

  return (
    <div className="relative">
      <div
        className="absolute right-2 top-2 cursor-pointer p-2 hover:bg-gray-100 rounded-md"
        onClick={handleCopy}
      >
        {isCopied ? (
          <AiOutlineCheck className="text-green-500" />
        ) : (
          <RiFileCopyLine className="text-gray-500" />
        )}
      </div>
      {
        sql
          ? <SyntaxHighlighter language="sql" style={vs} showLineNumbers={true}>
            {sql}
          </SyntaxHighlighter>
          : <div className='bg-white h-10 flex items-center justify-center text-gray-400 mt-4'>暂无数据</div>
      }

    </div>
  );
});

SQLView.displayName = 'SQLView';
export default SQLView;
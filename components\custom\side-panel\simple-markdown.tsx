'use client'

import React, { memo } from 'react';
import ReactMarkdown from 'react-markdown';
import RemarkGfm from 'remark-gfm';
import RemarkBreaks from 'remark-breaks';
import '../../../app/styles/markdown-report.scss'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { prism } from 'react-syntax-highlighter/dist/esm/styles/prism';
import MermaidDiagram from '@/components/chat/mermaid-diagram';

interface SimpleMarkdownProps {
  content: string;
}

const SyntaxCodeBlock = ({ children, className, inline, ...props }: { children: any; className?: string; inline?: boolean }) => {
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : '';

  // 1. 如果是 Mermaid 代码块，使用 MermaidDiagram 组件渲染
  if (language === 'mermaid') {
    return <MermaidDiagram chart={String(children)} />;
  }

  // 2. 如果是其他语言的代码块，使用语法高亮
  return !inline && match ? (
    <SyntaxHighlighter
      style={prism}
      language={language}
      PreTag="div"
      {...props}
    >
      {String(children).replace(/\n$/, '')}
    </SyntaxHighlighter>
  ) : (
    // 3. 如果是行内代码，正常渲染
    <code className={className} {...props}>
      {children}
    </code>
  );
};

// 缓存插件实例 - 只保留基本的文本处理插件
const remarkPlugins = [RemarkGfm, RemarkBreaks];

const SimpleMarkdown = memo(({ content }: SimpleMarkdownProps) => {
  if (!content) return null;

  return (
    <div className="markdown-body-report">
      <ReactMarkdown
        remarkPlugins={remarkPlugins}
        components={{
          code: SyntaxCodeBlock,
        } as any}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
});

SimpleMarkdown.displayName = 'SimpleMarkdown';

export default SimpleMarkdown;
'use client'

import './index.css'
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Toolt<PERSON>, TooltipArrow, TooltipContent, TooltipProvider, TooltipTrigger } from '@radix-ui/react-tooltip';
import { AiOutlineSync } from "react-icons/ai";
import { RiFileCopyLine } from "react-icons/ri";
import { AiOutlineLike, AiOutlineDislike, AiOutlineCheck } from "react-icons/ai";
import copy from 'copy-to-clipboard'
import { feedbackMessage } from '@/services/api';
import { useConversionStore } from '@/store/useConversion';

enum OparateType {
  REANSWER = '重新回答',
  COPY = '复制',
  LIKE = '喜欢',
  DISLIKE = '不喜欢'
}

const Operate = ({ item, isLast }: { item: any; isLast?: boolean }) => {
  const [isCopied, setIsCopied] = useState(false)

  const { content, id, feedback } = item

  const { changeFeedback, replyMessage } = useConversionStore((state) => ({
    changeFeedback: state.changeFeedback,
    replyMessage: state.replyMessage,
  }))

  // 使用 useMemo 优化操作列表
  const operateTypeList = useMemo(() => {
    const baseList = [
      {
        label: OparateType.REANSWER,
        icon: <AiOutlineSync />,
      },
      {
        label: OparateType.COPY,
        icon: isCopied ? <AiOutlineCheck /> : <RiFileCopyLine />
      },
      {
        label: OparateType.LIKE,
        icon: <AiOutlineLike fill={feedback === 1 ? 'red' : ''} />
      },
      {
        label: OparateType.DISLIKE,
        icon: <AiOutlineDislike fill={feedback === 2 ? '#0066FF' : ''} />
      }
    ]

    // 根据 isLast 过滤重新回答选项
    return isLast ? baseList : baseList.filter(item => item.label !== OparateType.REANSWER)
  }, [isCopied, isLast, feedback]) // 添加 isLast 依赖


  useEffect(() => {
    if (isCopied) {
      const timeout = setTimeout(() => {
        setIsCopied(false)
      }, 2000)

      return () => {
        clearTimeout(timeout)
      }
    }
  }, [isCopied])

  const hanlderClickType = (type: OparateType) => {
    switch (type) {
      case OparateType.REANSWER:
        replyMessage(item)
        break;
      case OparateType.COPY:
        if (!content || isCopied) return
        copy(content)
        setIsCopied(true)
        break;
      case OparateType.LIKE:
        changeFeedback(id, 1)
        break;
      case OparateType.DISLIKE:
        changeFeedback(id, 2)
        break;
    }
  }

  return (
    <TooltipProvider delayDuration={100}>
      <div className='mt-2 flex items-center space-x-2'>
        {operateTypeList.map((item, index) => (
          <div key={item.label} className="relative flex items-center"> {/* 新增包裹容器 */}
            {index === 2 && ( // 在第三个元素（点赞）前添加分割线
              <div className="absolute left-[-6px] h-4 w-px bg-gray-200"></div>
            )}
            <Tooltip>
              <TooltipTrigger>
                <div
                  onClick={() => hanlderClickType(item.label)}
                  className='cursor-pointer md-font-size text-gray-600 rounded-full bg-transparent hover:bg-gray-100 p-2'
                >
                  <span>{item.icon}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent className='TooltipContent' side='bottom' sideOffset={5}>
                {item.label}
                <TooltipArrow className="TooltipArrow" />
              </TooltipContent>
            </Tooltip>
          </div>
        ))}
      </div>
    </TooltipProvider>
  );
}

export default Operate;

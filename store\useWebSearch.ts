import { shallow } from "zustand/shallow";
import { createWithEqualityFn } from "zustand/traditional";

export type SearchResult = {
  title: string;
  url: string;
  favicon: string;
  context: string;
  order: number;
};

type WebSearchStore = {
  searchResults: SearchResult[];
  setSearchResults: (results: SearchResult[]) => void;
  clearSearchResults: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
};

export const useWebSearchStore = createWithEqualityFn<WebSearchStore>((set) => ({
  searchResults: [],
  setSearchResults: (results) => set({ searchResults: results }),
  clearSearchResults: () => set({ searchResults: [] }),
  isLoading: false,
  setIsLoading: (loading) => set({ isLoading: loading }),
}), shallow);
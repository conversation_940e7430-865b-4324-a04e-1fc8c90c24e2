import { cn } from '@/lib/utils';
import { useConversionStore } from '@/store/useConversion';
import React from 'react';
import { GoSidebarCollapse } from "react-icons/go";
import MobileHeader from './mobile-header';

const Header = () => {
  const { isCollapsed, changeCollapsed, curConversionName } = useConversionStore((state) => ({
    changeCollapsed: state.changeCollapsed,
    isCollapsed: state.isCollapsed,
    curConversionName: state.curConversionName,
  }))
  return (
    <div className='sticky px-5 left-0 top-0  flex lg:flex-row flex-col lg:items-center flex-shrink-0 lg:h-14 h-20 z-10 w-full bg-white/80 border-b'>
      <MobileHeader />
      <div className={cn('hidden lg:block w-fit mr-2', { 'lg:hidden': !isCollapsed })}>
        <GoSidebarCollapse className='text-xl cursor-pointer' onClick={() => changeCollapsed()} />
      </div>
      <div className='flex-1 flex items-center'>
        {curConversionName}
      </div>
    </div>
  );
}

export default Header;

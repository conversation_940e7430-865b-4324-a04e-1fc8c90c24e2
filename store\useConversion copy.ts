import { createWithEqualityFn } from 'zustand/traditional'
import { shallow } from 'zustand/shallow'
import { produce } from 'immer'
import { deleteConversation, feedbackMessage, fetchConversationList, fetchGetCommonConfig, fetchMessagesByConversionId, fetchStopChatMessage, updateConversation } from '@/services/api'
import { streamSSE } from '@/services/hanlderSSE'
import { USER_INFO } from '@/config'
import { persist, createJSONStorage } from 'zustand/middleware';
import { createIndexedDBStorage } from './indexedDBStorage'
import { set as idbSet } from 'idb-keyval'
import i18next from 'i18next';
import { useReportStore } from './useReport'

// 防止重复更新的标志
let isUpdating = false;

export interface Conversion {
  id: string
  user_id: string
  title?: string
  created_at: string
  messages: any[]
}

export interface Message {
  id: string | undefined
  role: 'user' | 'assistant'
  content?: string
  conversation_id?: string
  retrieval_sql?: string
  chart_data?: string
  reasoning_content?: string
  retrieval_result?: any[]
  answer?: string
  message_id?: string
  queried_table_comment?: string
  metadata?: any
  thought_data?: any
  thoughts?: any[]
  isThinking?: boolean  // 新增：是否正在思考状态
  final_report?: string  // 新增：最终报告HTML内容
  hasReport?: boolean
  get_final_report?: boolean
}

export interface ConversionState {
  queryParams: {
    page: number
    page_num: number
  }
  tempFinalReportMap: Record<string, string>; // message_id -> 完整拼接中的 report
  suggestPage: number
  isMobileCollapsed: boolean
  isCollapsed: boolean
  isChatStarted: boolean
  curConversionId: string | null
  curConversionName: string | null
  conversions: Conversion[]
  showSideBar: boolean
  initLoading: boolean
  messages: Message[]
  messageLoading: boolean
  abortController: (() => void) | null
  isResponding: boolean  // 新增流式输出状态
  currentTaskId: string | null;
  systemConfig: any;
  setSuggestPage: (page: number) => void
  chatType: string
  setChatType: (type: string) => void
}

const getTranslation = (key: string) => {
  return i18next.t(key);
};

const initConversionData = {
  isMobileCollapsed: true,
  queryParams: { page: 1, page_num: 100 },
  suggestPage: 1,
  isCollapsed: false,
  isChatStarted: false,
  curConversionId: '-1',
  curConversionName: getTranslation('common.sidebar.add_conversation'),
  conversions: [] as Conversion[],
  showSideBar: true,
  initLoading: true,
  messageLoading: false,
  messages: [],
  abortController: null,
  isResponding: false,  // 新增初始值
  currentTaskId: null,
  systemConfig: {},
  chatType: 'default',
  tempFinalReportMap: {},
}

interface ConversionActions {
  initConversion: () => void
  addConversion: () => void
  removeConversion: (id: string) => void
  updateConversionName: (id: string, name: string) => void
  changeConversion: (id: string) => void
  fetchHistroyMessages: (id: string) => void
  changeCollapsed: () => void
  addMessage: (question: string) => void
  updateStreamMessage: (message: Message, conversationId: string) => void
  updateConversionId: (oldId: string, newId: string) => void
  setAbortController: (controller: (() => void) | null) => void;
  handleSendMessage: (question: string, message_id?: string) => Promise<void>;
  startChat: () => void;
  updateLastMessageId: (conversation_id: string) => void;
  changeFeedback: (id: string, feedback: number) => void;
  replyMessage: (message: Message) => void;
  setIsMobileCollapsed: (isMobileCollapsed: boolean) => void;
  updateName: (id: string) => void;
  stopChatMessage: () => void;
  getFinalReport: (messageId: string) => void;
}

type ConversionStore = ConversionState & ConversionActions

export const useConversionStore = createWithEqualityFn(
  persist(
    (set, get): ConversionStore => (
      {
        ...initConversionData,
        setChatType: (type: string): void => {
          const { chatType } = get();
          if (type === chatType) {
            // 再次点击已选项，切回默认
            set({ chatType: 'default' });
          } else {
            set({ chatType: type });
          }
        },
        addConversion: (): void => {
          const hasNewConversion = get().conversions.some((c) => c.id === '-1');
          if (hasNewConversion) return;

          set(produce(state => {
            state.conversions.unshift({
              id: '-1',
              user_id: USER_INFO.id,
              name: getTranslation('common.sidebar.add_conversation'),
              created_at: new Date().toISOString(),
              messages: [],
            });
            state.curConversionId = '-1';
            state.curConversionName = getTranslation('common.sidebar.add_conversation');
            state.isChatStarted = false;
            state.messageLoading = false;
          }));
        },
        changeConversion: (id: string): void => {
          const store = get()
          const currentConversion = store.conversions.find((conversion) => conversion.id === id)

          set(produce(state => {
            state.messageLoading = true;
          }));

          // 处理新会话的情况
          if (id === '-1') {
            if (currentConversion) {
              // 已存在新会话,更新状态
              set(produce(state => {
                state.curConversionId = id
                state.curConversionName = currentConversion.title ?? getTranslation('common.sidebar.add_conversation')

                // 检查是否有历史消息
                const hasMessages = currentConversion.messages && currentConversion.messages.length > 0
                if (hasMessages) {
                  state.messages = currentConversion.messages
                  state.isChatStarted = true
                } else {
                  state.messages = []
                  state.isChatStarted = false
                }
                state.messageLoading = false
              }))
            } else {
              // 不存在新会话,创建一个
              store.addConversion()
            }
            return
          }

          // 处理已有会话的情况
          if (!currentConversion) return

          // 更新基础状态
          set(produce(state => {
            state.curConversionId = id
            state.curConversionName = currentConversion.title ?? getTranslation('common.sidebar.add_conversation')
          }))

          // 处理消息加载
          const hasMessages = currentConversion.messages && currentConversion.messages.length > 0
          if (hasMessages) {
            setTimeout(() => {
              set(produce(state => {
                state.messages = currentConversion.messages
                state.isChatStarted = true
                state.messageLoading = false
              }))
            }, 0);
          } else {
            set(produce(state => {
              state.messages = []
              state.messageLoading = true
            }))
            store.fetchHistroyMessages(id)
          }
        },
        setSuggestPage: (page: number): void => {
          set(produce(state => {
            state.suggestPage = page
          }));
        },
        removeConversion: async (id: string): Promise<void> => {
          const store = get();
          const { conversions } = store;

          // 找到要删除的会话索引
          const deleteIndex = conversions.findIndex(c => c.id === id);
          if (deleteIndex === -1) return; // 如果找不到会话，直接返回

          // 处理唯一会话情况
          if (conversions.length === 1) {
            const newConversation = {
              id: '-1',
              user_id: USER_INFO.id,
              title: getTranslation('common.sidebar.add_conversation'),
              created_at: new Date().toISOString(),
              messages: [], // 添加messages属性
            };

            try {
              // 先尝试删除旧会话
              if (conversions[0].id !== '-1') {
                await deleteConversation(conversions[0].id);
              }

              set(produce(state => {
                state.conversions = [newConversation];
                state.curConversionId = newConversation.id;
                state.curConversionName = newConversation.title;
                state.messages = [];
                state.isChatStarted = false;
              }));
            } catch (error) {
              console.error('删除会话失败:', error);
            }
            return;
          }

          // 计算新的当前会话 - 优先选择前一个会话
          const newIndex = deleteIndex === 0 ? 1 : deleteIndex - 1;
          const conversationToDelete = conversions[deleteIndex];
          const newConversion = conversions[newIndex];

          try {
            // 先删除后端数据
            if (conversationToDelete.id !== '-1') {
              await deleteConversation(conversationToDelete.id);
            }

            set(produce(state => {
              state.conversions.splice(deleteIndex, 1);
              state.curConversionId = newConversion.id;
              state.curConversionName = newConversion.title ?? getTranslation('common.sidebar.add_conversation');
              state.messages = [];
            }));

            // 如果切换到了非新会话，则获取历史消息
            if (newConversion.id !== '-1') {
              await store.changeConversion(newConversion.id);
            } else {
              set(produce(state => {
                state.isChatStarted = false;
              }))
            }
          } catch (error) {
            console.error('删除会话过程中出错:', error);
          }
        },
        updateConversionName: async (id: string, name: string): Promise<void> => {
          set(
            produce((state: ConversionState) => {
              const conversion = state.conversions.find((conversion) => conversion.id === id)
              if (conversion) conversion.title = name
            })
          );
          updateConversation(id, name || getTranslation('common.sidebar.add_conversation'));
        },
        initConversion: async (): Promise<any> => {
          const store = get();
          try {
            // 获取系统配置
            const { data: configData } = await fetchGetCommonConfig() as any;
            // 获取会话列表
            const { data } = await fetchConversationList(store.queryParams) as { data: Conversion[] };

            set(
              produce((state: ConversionState) => {
                state.systemConfig = configData;
                state.conversions = data.map((item) => ({
                  ...item,
                  messages: [],
                }));

                // 如果没有任何会话，创建一个新会话
                if (data.length === 0) {
                  const newConversation = {
                    id: '-1',
                    user_id: USER_INFO.id,
                    title: getTranslation('common.sidebar.add_conversation'),
                    created_at: new Date().toISOString(),
                    messages: [],
                  };
                  state.conversions = [newConversation];
                  state.curConversionId = '-1';
                  state.curConversionName = getTranslation('common.sidebar.add_conversation');
                  state.isChatStarted = false;
                } else {
                  // 有会话时，选择第一个会话
                  state.curConversionId = data[0].id;
                  state.curConversionName = data[0].title ?? getTranslation('common.sidebar.add_conversation');
                  state.isChatStarted = true;
                }
              })
            );

            // 如果有会话，加载第一个会话的消息
            if (data.length > 0) {
              await get().changeConversion(data[0].id);
            }

            // 完成初始化，关闭加载状态
            set(
              produce((state: ConversionState) => {
                state.initLoading = false;
              })
            );

            return data;
          } catch (error) {
            console.error('初始化会话失败:', error);
            // 出错时也要关闭加载状态
            set(
              produce((state: ConversionState) => {
                state.initLoading = false;
              })
            );
            throw error;
          }
        },
        fetchHistroyMessages: async (id: string): Promise<void> => {
          try {
            const { data: { messages } } = await fetchMessagesByConversionId(id) as { data: { messages: any[] } };

            // 处理历史消息中的思维链数据
            const processedMessages = messages?.map((message: any) => {
              if (message.role === 'assistant' && message.thoughts_info) {
                // 将 thoughts_info 转换为 thoughts 字段以兼容现有组件
                const thoughts = message?.thoughts_info?.thought_process?.map?.((item: any) => ({
                  // ...(item?.thought_data || {})
                  ...(item || {})
                })) || [];
                const groupMap: any = {}
                thoughts.forEach((thought: any) => {
                  const number = thought.thought_number;
                  if (groupMap[number]) {
                    // 若已存在同编号，合并属性，这里简单处理，后者覆盖前者，可按需调整合并逻辑
                    groupMap[number] = { ...groupMap[number], ...thought };
                  } else {
                    // 不存在则直接存入
                    groupMap[number] = thought;
                  }
                });

                // 将分组后的结果转成数组，得到合并后的 thoughts 数组
                const mergedThoughts = Object.values(groupMap);
                return {
                  ...message,
                  thoughts: mergedThoughts,
                  final_report: message?.thoughts_info?.final_report,
                  hasReport: message?.thoughts_info?.final_report !== null,
                  // 保留原始数据
                  thoughts_info: message?.thoughts_info
                };
              }
              return message;
            }) || [];

            set(produce(state => {
              // 更新会话列表中的消息
              const conversation = state.conversions.find((c: any) => c.id === id);
              if (conversation) {
                conversation.messages = processedMessages;
              }
              // 更新当前显示的消息
              state.messages = processedMessages;
              state.messageLoading = false;
              state.isChatStarted = true;
            }));
          } catch (error) {
            console.error('消息加载失败:', error);
            set(produce(state => {
              state.messageLoading = false;
            }));
          }
        },
        changeCollapsed: (): void => {
          set(
            produce((state: ConversionState) => {
              state.isCollapsed = !state.isCollapsed
            })
          )
        },
        addMessage: (question: string): void => {
          const currentId = get().curConversionId
          if (!currentId) return

          const questionMessage: Message = {
            id: `temp_${Date.now()}_q`,
            content: question,
            role: 'user',
            conversation_id: currentId
          }

          const answerMessage: Message = {
            id: `temp_${Date.now()}_a`,
            content: '',
            role: 'assistant',
            conversation_id: currentId
          };

          set(produce(state => {
            const conversation = state.conversions.find((c: any) => c.id === currentId);
            if (conversation) {
              conversation.messages.push(questionMessage, answerMessage);
            }
            // state.messages.push(questionMessage, answerMessage);
          }));
        },
        updateStreamMessage: (message: Message, conversationId: string): void => {
          // 防止重复更新
          if (isUpdating) {
            return;
          }

          const state = get();
          const currentId = state.curConversionId;

          // 防止无效调用
          if (!message || !conversationId) {
            console.warn('updateStreamMessage: Invalid message or conversationId');
            return;
          }

          // 只有当消息属于当前会话时才更新
          if (currentId === conversationId || (currentId === '-1' && conversationId !== '-1')) {
            isUpdating = true;

            try {
              set(produce(draft => {
                // 如果当前是新对话且返回的是实际conversationId，在同一个produce中更新ID
                if (currentId === '-1' && conversationId !== '-1') {
                  // 更新会话列表中的ID
                  const conversion = draft.conversions.find((c: Conversion) => c.id === '-1');
                  if (conversion) {
                    conversion.id = conversationId;
                  }
                  // 更新当前会话ID
                  draft.curConversionId = conversationId;
                }

                // 查找当前会话（使用更新后的ID）
                const targetConversationId = currentId === '-1' && conversationId !== '-1' ? conversationId : currentId;
                const currentConversation = draft.conversions.find((c: any) => c.id === targetConversationId);

                if (!currentConversation || !currentConversation.messages) {
                  console.warn('updateStreamMessage: No conversation or messages found');
                  return;
                }

                const lastMessage: Message = currentConversation.messages[currentConversation.messages.length - 1];

                if (lastMessage && lastMessage.role === 'assistant') {
                  lastMessage.content = message.answer;
                  lastMessage.chart_data = message.chart_data
                  lastMessage.queried_table_comment = message.queried_table_comment
                  lastMessage.metadata = message.metadata
                  lastMessage.get_final_report = message.get_final_report
                  // if (message.final_report) {
                  //   const current = draft.tempFinalReportMap[message.message_id!] || '';
                  //   draft.tempFinalReportMap[message.message_id!] = current + message.final_report;

                  //   if (!lastMessage.hasReport) lastMessage.hasReport = true;
                  // }
                  if (message.thought_data) {
                    // 保存完整的 thought_data 对象，后续可能有用
                    // lastMessage.thought_data = message.thought_data;

                    // 为思考链组件转换格式
                    if (Array.isArray(message.thought_data)) {
                      // 如果是数组，直接使用
                      lastMessage.thoughts = [...message.thought_data];
                    } else if (typeof message.thought_data === 'object' && message.thought_data.thought) {
                      // 如果是单个思考对象，转换为数组格式
                      const thoughtItem = {
                        ...message.thought_data
                      };

                      // 如果已有思考链，检查是否已存在相同的思考步骤
                      if (lastMessage.thoughts && Array.isArray(lastMessage.thoughts)) {
                        const existingIndex = lastMessage.thoughts.findIndex(
                          (t: any) => t.thought_number === thoughtItem.thought_number
                        );

                        if (existingIndex >= 0) {
                          // 直接覆盖相同步数的思考内容
                          lastMessage.thoughts[existingIndex] = thoughtItem;
                        } else {
                          // 添加新的思考步骤
                          lastMessage.thoughts = [...lastMessage.thoughts, thoughtItem];
                        }
                      } else {
                        lastMessage.thoughts = [thoughtItem];
                      }
                    }

                    // 判断思考是否完成：有最终答案且最后一个思考步骤的 next_thought_needed 为 false
                    const hasAnswer = message.answer && message.answer.trim().length > 0;
                    const lastThought = lastMessage.thoughts && lastMessage.thoughts.length > 0
                      ? lastMessage.thoughts[lastMessage.thoughts.length - 1]
                      : null;
                    const isThinkingComplete = hasAnswer || (lastThought && !lastThought.next_thought_needed);

                    // 设置思考状态
                    lastMessage.isThinking = !isThinkingComplete;
                  }

                  if (lastMessage.id?.startsWith('temp_')) {
                    lastMessage.id = message.message_id;
                    lastMessage.conversation_id = conversationId;
                    lastMessage.retrieval_result = message.retrieval_result;
                    lastMessage.retrieval_sql = message.retrieval_sql;
                  }
                }
              }));
            } catch (error) {
              console.error('updateStreamMessage error:', error);
            } finally {
              isUpdating = false;
            }
          }
        },
        updateConversionId(oldId, newId) {
          set(produce(state => {
            // 更新会话列表中的ID
            const conversion = state.conversions.find((c: Conversion) => c.id === oldId);
            if (conversion) {
              conversion.id = newId;
            }
            // 更新当前会话ID
            if (state.curConversionId === oldId) {
              state.curConversionId = newId;
            }
          }));
        },
        setAbortController: (controller) => {
          set(produce(state => {
            state.abortController = controller;
          }));
        },
        handleSendMessage: async (message: string, message_id: string = '') => {
          const store = get()

          // 取消之前的请求
          if (store.abortController) {
            store.abortController()
            store.setAbortController(null)
          }

          try {
            store.addMessage(message)
            // 设置开始流式输出状态
            set(produce(state => {
              state.isResponding = true;
            }));

            const queryData = {
              conversation_id: store.curConversionId === '-1' ? '' : store.curConversionId,
              query: message,
              message_id,
            }

            let chatUrl = ''

            switch (store.chatType) {
              case 'kb':
                chatUrl = `/api/chat/chat2knowledge`;
                break;
              case 'web':
                chatUrl = `/api/chat/chat2web`;
                break;
              case 'process':
                chatUrl = `/api/chat/deepReserach`;
                break;
              default:
                chatUrl = `/api/chat/chat2db`;
            }

            const abortController = await streamSSE(`${process.env.NEXT_PUBLIC_API_URL}${chatUrl}`, queryData, {
              onMessage: (content: any) => {
                try {
                  const data = content
                  if (data.task_id && data.task_id !== store.currentTaskId) {
                    set(produce(state => {
                      state.currentTaskId = data.task_id;
                    }));
                  }
                  store.updateStreamMessage(data, data.conversation_id || store.curConversionId)
                } catch (error) {
                  console.error('Failed to parse SSE message:', error);
                }
              },

              onError: (error) => {
                console.error('Stream error:', error);
                set(produce(state => {
                  state.messageLoading = false;
                  state.isResponding = false;  // 设置结束流式输出状态
                }));
              },
              onFinish: (content: any) => {
                set(produce(state => {
                  state.messageLoading = false;
                  state.isResponding = false;  // 设置结束流式输出状态
                }));
                const data = content

                store.setAbortController(null);
                // store.updateLastMessageId(data.conversation_id || store.curConversionId)
                data.conversation_id && store.updateName(data.conversation_id)
                data.get_final_report && useReportStore.getState().fetchFinalReport(data.message_id)

                // const current = get(); // ← 拿最新的 store 状态

                // const finalReport = current.tempFinalReportMap[data.message_id];
                // if (data.message_id && finalReport) {
                //   set(produce(state => {
                //     const currentConversation = state.conversions.find((c: any) => c.id === data.conversation_id);
                //     if (!currentConversation) return;

                //     const targetMsg = currentConversation.messages.find((m: any) => m.id === data.message_id);
                //     if (targetMsg) {
                //       targetMsg.final_report = finalReport;
                //     }

                //     // 清除缓存
                //     delete state.tempFinalReportMap[data.message_id];
                //   }));
                // }
              }
            })

            store.setAbortController(abortController);

          } catch (error) {
            console.error('Send message failed:', error);
            set(produce(state => {
              state.messageLoading = false;
              state.isResponding = false;  // 错误时也需要重置流式输出状态
            }));
          }
        },
        updateName: async (conversation_id: string) => {
          const { data } = await fetchMessagesByConversionId(conversation_id) as { data: any }
          const conversionName = data.title;
          set(produce(state => {
            const conversation = state.conversions.find((c: any) => c.id === conversation_id);
            if (conversation) {
              conversation.title = conversionName;
              state.curConversionName = conversionName;
            }
          }))
        },
        updateLastMessageId: async (conversation_id: string) => {
          const store = get()
          const assistantMessage = store.messages[store.messages.length - 1]
          if (assistantMessage && assistantMessage.role === 'assistant' && conversation_id !== '-1') {
            const { data: { messages, title } } = await fetchMessagesByConversionId(conversation_id!) as { data: { messages: any[], title: string } };
            if (title) {
              store.updateConversionName(conversation_id!, title)
            }

            const serverMessage = messages
              .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

            // 处理最新消息中的思维链数据
            const processedLatestMessages = serverMessage.slice(0, 2).map((message: any) => {
              if (message.role === 'assistant' && message.thoughts_info) {
                return {
                  ...message,
                  thoughts: message.thoughts_info,
                  thoughts_info: message.thoughts_info
                };
              }
              return message;
            });

            if (processedLatestMessages) {
              set(
                produce(state => {
                  // 替换最后两条消息
                  state.messages = state.messages.slice(0, -2).concat(processedLatestMessages);
                  // const assistantMessageIndex = state.messages.findIndex((m: Message) => m.id === assistantMessage.id);
                  // if (assistantMessageIndex !== -1) {
                  //   state.messages[tempMessageIndex] = {
                  //     ...state.messages[tempMessageIndex],
                  //     id: serverMessage.id, // 更新为服务器生成的ID
                  //     content: serverMessage.content,
                  //     retrieval_sql: serverMessage.retrieval_sql,
                  //     chart_data: serverMessage.chart_data,
                  //     retrieval_result: serverMessage.retrieval_result,
                  //     feedback: serverMessage.feedback
                  //   }
                  // }
                })
              )
            }
          }
        },
        changeFeedback: async (id: string, feedback: number) => {
          const store = get()
          const currentId = store.curConversionId
          if (!currentId) return

          // 从当前会话中查找消息
          const currentConversation = store.conversions.find(c => c.id === currentId)
          if (!currentConversation) return

          const message = currentConversation.messages.find(m => m.id === id && m.role === 'assistant')
          if (message) {
            set(
              produce(state => {
                const conversation = state.conversions.find((c: any) => c.id === currentId)
                if (conversation) {
                  const messageIndex = conversation.messages.findIndex((m: any) => m.id === id && m.role === 'assistant')
                  if (messageIndex !== -1) {
                    conversation.messages[messageIndex] = {
                      ...conversation.messages[messageIndex],
                      feedback
                    }
                  }
                }
              })
            )
            feedbackMessage(id, feedback)
          }
        },
        startChat: () => {
          set(produce(state => {
            state.isChatStarted = true;
          }));
        },
        // 重新回答
        replyMessage: async (message: Message) => {
          const store = get()
          const { id } = message
          const currentId = store.curConversionId
          if (!currentId) return

          // 从当前会话中查找消息
          const currentConversation = store.conversions.find(c => c.id === currentId)
          if (!currentConversation) return

          const queryMessage = currentConversation.messages[currentConversation.messages.length - 2]
          const answerMessage = currentConversation.messages[currentConversation.messages.length - 1]

          // 如果没有找到对应的消息，直接返回
          if (!queryMessage || !answerMessage) {
            return
          }

          // 从当前会话中删除消息
          set(produce(state => {
            const conversation = state.conversions.find((c: any) => c.id === currentId)
            if (conversation) {
              conversation.messages = conversation.messages.filter((m: any) =>
                m.id !== queryMessage.id && m.id !== answerMessage.id
              )
            }
          }))

          // 重新发送消息
          await store.handleSendMessage(queryMessage.content!, id)
        },
        setIsMobileCollapsed: (isCollapsed: boolean) => {
          set(produce(state => {
            state.isMobileCollapsed = isCollapsed;
          }));
        },
        stopChatMessage: async () => {
          const store = get()
          if (!store.currentTaskId) return
          if (store.abortController) {
            store.abortController()
            store.setAbortController(null)
          }

          try {
            await fetchStopChatMessage(store.currentTaskId)
            set(produce(state => {
              state.isResponding = false;
              state.currentTaskId = null;
              state.messageLoading = false;
            }));
          } catch (error) {
            console.error('停止对话失败:', error);
          }
        },
        getFinalReport: async (messageId: string) => {
          const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/chat/generateHtmlReport`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              message_id: messageId,
            }),
          })
          const data = await res.json()
          return data
        }
      }
    ),
    {
      name: 'conversion-storage', // 存储的键名
      storage: createJSONStorage(() => createIndexedDBStorage()),
      partialize: (state) => ({
        conversions: state.conversions
      }),
    }
  ),
  shallow
);

// 在初始化时添加数据迁移逻辑
const migrateFromLocalStorage = async () => {
  // 检查是否在浏览器环境
  if (typeof window === 'undefined') return;

  const localData = localStorage.getItem('conversion-storage');
  if (localData) {
    try {
      const { state } = JSON.parse(localData);
      await idbSet('conversion-storage', { state });
      localStorage.removeItem('conversion-storage');
    } catch (error) {
      console.error('数据迁移失败:', error);
    }
  }
};

// 在应用启动时调用，同样需要确保在客户端环境
if (typeof window !== 'undefined') {
  migrateFromLocalStorage();
}

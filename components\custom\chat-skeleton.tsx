import React from 'react';
import { Skeleton } from "@/components/ui/skeleton"

const ChatSkeleton = () => {
  return (
    <div className="flex-1 flex flex-col p-6 bg-white w-full">
        {/* 对话内容区域 */}
        <div className="flex-1 lg:w-[794px] max-w-full overflow-x-hidden w-full mx-auto space-y-6 p-4">
          {/* 用户问题 */}
          <div className="flex items-start space-x-4 justify-end">
            <div className="space-y-2">
              <Skeleton className="h-4 w-[300px] bg-[#EAEBED]" />
              <Skeleton className="h-4 w-[400px] bg-[#EAEBED]" />
            </div>
            <Skeleton className="h-10 w-10 rounded-full bg-[#EAEBED]" />
          </div>

          {/* AI回答 */}
          <div className="flex items-start space-x-4">
            <Skeleton className="h-10 w-10 rounded-full bg-[#EAEBED]" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[500px] bg-[#EAEBED]" />
              <Skeleton className="h-4 w-[400px] bg-[#EAEBED]" />
              <Skeleton className="h-4 w-[450px] bg-[#EAEBED]" />
            </div>
          </div>

          {/* 重复的对话模式 */}
          {Array.from({ length: 2 }).map((_, index) => (
            <React.Fragment key={index}>
              <div className="flex items-start space-x-4 justify-end">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[350px] bg-[#EAEBED]" />
                  <Skeleton className="h-4 w-[250px] bg-[#EAEBED]" />
                </div>
                <Skeleton className="h-10 w-10 rounded-full bg-[#EAEBED]" />
              </div>
              <div className="flex items-start space-x-4">
                <Skeleton className="h-10 w-10 rounded-full bg-[#EAEBED]" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[400px] bg-[#EAEBED]" />
                  <Skeleton className="h-4 w-[450px] bg-[#EAEBED]" />
                </div>
              </div>
            </React.Fragment>
          ))}
        </div>

        {/* 底部输入框 */}
        <div className="mt-6 lg:w-[794px] max-w-full overflow-x-hidden w-full mx-auto space-y-6 p-4">
          <Skeleton className="h-20 w-full bg-[#EAEBED] rounded-lg" />
        </div>
      </div>
  );
}

export default ChatSkeleton;
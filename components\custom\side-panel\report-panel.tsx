"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, ChevronLeft, ChevronRight } from "lucide-react";
import { useReportStore } from "@/store/useReport";

type Chapter = {
  url: string;
  title: string;
  summary: string;
};
type ReportData = {
  title: string;
  summary: string;
  chapters: Chapter[];
  tocUrl?: string;
};

export function ReportPanel() {
  const { currentReport, isReportLoading } = useReportStore((s) => ({
    currentReport: s.currentReport,
    isReportLoading: s.isReportLoading,
  }));
  const data: ReportData | null = currentReport
    ? {
        title: currentReport.title,
        summary: currentReport.summary || "",
        chapters: currentReport.chapters || [],
        tocUrl: currentReport.tocUrl,
      }
    : null;
  const [search, setSearch] = useState("");
  const [mode, setMode] = useState<"list" | "read">("list"); // 列表 | 阅读
  const [currentIndex, setCurrentIndex] = useState<number | null>(null);
  const [iframeHeight, setIframeHeight] = useState(600);
  const iframeRef = useRef<HTMLIFrameElement | null>(null);

  /* 过滤章节 -------------------------------------------------------- */
  const filteredChapters = (data?.chapters || []).filter(
    (c) =>
      c.title.toLowerCase().includes(search.toLowerCase()) ||
      c.summary.toLowerCase().includes(search.toLowerCase())
  );

  /* 高亮 ----------------------------------------------------------- */
  const highlight = (text: string, key: string) => {
    if (!key) return text;
    return text.replace(
      new RegExp(`(${key})`, "gi"),
      '<mark class="bg-yellow-300">$1</mark>'
    );
  };

  /* 点击章节 → 进入阅读模式 ---------------------------------------- */
  const enterChapter = (idx: number) => {
    setCurrentIndex(idx);
    setMode("read");
    setIframeHeight(600); // 初始高度
  };

  /* 返回目录 ------------------------------------------------------- */
  const backToList = () => setMode("list");

  /* 上下章 --------------------------------------------------------- */
  const goPrev = () => {
    if (currentIndex === null || currentIndex === 0) return;
    enterChapter(currentIndex - 1);
  };
  const goNext = () => {
    if (currentIndex === null || !data || currentIndex === (data.chapters?.length || 0) - 1)
      return;
    enterChapter(currentIndex + 1);
  };

  /* 计算iframe高度，占满可用空间 ---------------------------------- */
  useEffect(() => {
    const calculateHeight = () => {
      // 获取窗口高度
      const windowHeight = window.innerHeight;
      // 计算顶部导航栏高度（sticky元素）
      const navHeight = 60; // 预估导航栏高度
      // 计算上下边距
      const marginHeight = 48; // mb-6 (24px) + 其他边距 (24px)
      // 计算最终高度
      const newHeight = windowHeight - navHeight - marginHeight;
      setIframeHeight(Math.max(newHeight, 400)); // 最小高度400px
    };

    // 初始计算
    calculateHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', calculateHeight);
    
    // 同时监听iframe内部消息
    const messageHandler = (e: MessageEvent) => {
      if (e.data?.type === "resize" && typeof e.data.height === "number") {
        // 如果iframe内部有更高的内容高度，使用内部高度
        const contentHeight = e.data.height;
        const windowHeight = window.innerHeight;
        const navHeight = 60;
        const marginHeight = 48;
        const maxHeight = windowHeight - navHeight - marginHeight;
        
        // 使用内容高度和最大可用高度的较小值
        setIframeHeight(Math.min(contentHeight, maxHeight));
      }
    };
    
    window.addEventListener("message", messageHandler);
    
    return () => {
      window.removeEventListener('resize', calculateHeight);
      window.removeEventListener("message", messageHandler);
    };
  }, []);

  // 加载态与空态
  if (isReportLoading) {
    return (
      <div className="min-h-screen bg-gray-50 text-gray-800 flex items-center justify-center">
        <div className="text-gray-500">报告生成中，请稍候...</div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 text-gray-800 flex items-center justify-center">
        <div className="text-gray-400">暂无报告数据</div>
      </div>
    );
  }

  // 如果没有章节但有 tocUrl，直接进入阅读模式展示目录页
  const hasChapters = Array.isArray(data.chapters) && data.chapters.length > 0;
  const shouldShowTocOnly = !hasChapters && !!data.tocUrl;

  useEffect(() => {
    if (shouldShowTocOnly) {
      setMode("read");
      setCurrentIndex(0);
    }
  }, [shouldShowTocOnly]);

  /* ----------------------------------------------------------------
                              渲染
  ------------------------------------------------------------------ */
  return (
    <div className="min-h-screen bg-gray-50 text-gray-800">
      <div className="h-screen flex flex-col">
        {/* 列表模式 --------------------------------------------------- */}
        {mode === "list" && hasChapters && (
          <div className="flex-1 overflow-y-auto px-4 py-6">
            <div className="max-w-5xl mx-auto">
              <header className="text-center mb-10">
                <h1 className="text-3xl md:text-4xl font-bold text-blue-700 mb-4">
                  {data.title || "深度研究报告"}
                </h1>
                <div className="bg-blue-50 p-5 rounded-lg border-l-4 border-blue-500 text-left">
                  <h2 className="font-semibold text-blue-700 mb-1">报告摘要</h2>
                  <p className="text-gray-700 leading-relaxed">{data.summary || ""}</p>
                </div>
              </header>

              <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredChapters.map((c, i) => {
                  const originalIndex = data.chapters.findIndex(
                    (ch) => ch.url === c.url
                  );
                  return (
                    <button
                      key={i}
                      onClick={() => enterChapter(originalIndex)}
                      className="text-left block bg-white rounded-lg p-6 shadow hover:shadow-lg hover:-translate-y-1 transition-all border-l-4 border-blue-500"
                    >
                      <span className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded mb-2">
                        第{originalIndex + 1}章
                      </span>
                      <h3
                        className="text-lg font-semibold text-gray-800 mb-2"
                        dangerouslySetInnerHTML={{
                          __html: highlight(c.title, search),
                        }}
                      />
                      <p
                        className="text-sm text-gray-600"
                        dangerouslySetInnerHTML={{
                          __html: highlight(c.summary, search),
                        }}
                      />
                    </button>
                  );
                })}
                {filteredChapters.length === 0 && (
                  <div className="col-span-2 text-center text-gray-500">
                    暂无匹配章节
                  </div>
                )}
              </section>

              {/* 页脚 ------------------------------------------------------- */}
              <footer className="text-center text-gray-500 text-sm border-t pt-6 mt-12">
                <p>报告生成日期：{new Date().toLocaleDateString("zh-CN")}</p>
                <p className="mt-1">© 2024 系统A 质量分析团队 · 保留所有权利</p>
              </footer>
            </div>
          </div>
        )}

        {/* 阅读模式 --------------------------------------------------- */}
        {mode === "read" && (
          <div className="h-screen flex flex-col">
            {/* 顶部导航栏 - 固定吸附在顶部 */}
            <div className="sticky top-0 z-50 h-16 bg-white/95 backdrop-blur border-b px-4 flex items-center justify-between shadow-sm">
              <button
                onClick={backToList}
                className="flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors font-medium"
                disabled={!hasChapters}
              >
                <ArrowLeft className="w-5 h-5" />
                <span>返回目录</span>
              </button>
              <h2 className="text-lg font-semibold text-gray-800 truncate px-4">
                {hasChapters && currentIndex !== null
                  ? data.chapters[currentIndex].title
                  : data.title || "报告目录"}
              </h2>
              <div className="flex items-center gap-2">
                <button
                  onClick={goPrev}
                  disabled={!hasChapters || currentIndex === 0}
                  className={`flex items-center gap-1 px-3 py-1.5 rounded border text-sm transition-colors ${
                    !hasChapters || currentIndex === 0
                      ? "opacity-40 cursor-not-allowed border-gray-200"
                      : "hover:bg-gray-50 border-gray-300"
                  }`}
                >
                  <ChevronLeft className="w-4 h-4" />
                  <span>上一章</span>
                </button>
                <button
                  onClick={goNext}
                  disabled={!hasChapters || currentIndex === (data.chapters?.length || 0) - 1}
                  className={`flex items-center gap-1 px-3 py-1.5 rounded border text-sm transition-colors ${
                    !hasChapters || currentIndex === (data.chapters?.length || 0) - 1
                      ? "opacity-40 cursor-not-allowed border-gray-200"
                      : "hover:bg-gray-50 border-gray-300"
                  }`}
                >
                  <span>下一章</span>
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* 正文区域 - iframe 可滚动 */}
            <div className="flex-1 overflow-hidden">
              <div className="h-full bg-white rounded-lg shadow border overflow-y-auto">
                {shouldShowTocOnly ? (
                  <iframe
                    ref={iframeRef}
                    src={data.tocUrl}
                    width="100%"
                    height={iframeHeight}
                    className="block w-full"
                    style={{ minHeight: '100%' }}
                    onLoad={() => {
                      iframeRef.current?.contentWindow?.postMessage(
                        { type: "askHeight" },
                        "*"
                      );
                    }}
                  />
                ) : (
                  currentIndex !== null && hasChapters && (
                    <iframe
                      ref={iframeRef}
                      src={data.chapters[currentIndex].url}
                      width="100%"
                      height={iframeHeight}
                      className="block w-full"
                      style={{ minHeight: '100%' }}
                      onLoad={() => {
                        /* 向 iframe 发消息索取高度 */
                        iframeRef.current?.contentWindow?.postMessage(
                          { type: "askHeight" },
                          "*"
                        );
                      }}
                    />
                  )
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

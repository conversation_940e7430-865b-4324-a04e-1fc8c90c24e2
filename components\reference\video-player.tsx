import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Lu<PERSON>ause,
  LuVolume2,
  LuVolumeX,
  LuMaximize,
  Lu<PERSON>inimize,
  <PERSON><PERSON>kipBack,
  LuSkipForward,
  LuSettings,
  LuDownload
} from "react-icons/lu";

interface VideoPlayerProps {
  reference: {
    source: string;
    start_ms?: number;
    end_ms?: number;
    [key: string]: any;
  };
  autoPlay?: boolean;
  showControls?: boolean;
  customStyles?: {
    primaryColor?: string;
    backgroundColor?: string;
    controlsHeight?: string;
  };
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  reference,
  autoPlay = false,
  customStyles = {}
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const volumeRef = useRef<HTMLDivElement>(null);

  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [showControls, setShowControlsVisible] = useState(true);
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null);

  const {
    primaryColor = "#3b82f6",
    backgroundColor = "rgba(0, 0, 0, 0.7)",
    controlsHeight = "60px"
  } = customStyles;

  // 视频事件处理
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
      setIsLoading(false);

      // 如果有开始时间，跳转到指定时间
      if (reference.start_ms) {
        const startTime = reference.start_ms / 1000;
        video.currentTime = startTime;
        setCurrentTime(startTime);
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);

      // 如果有结束时间，到达结束时间时暂停
      // if (reference.end_ms) {
      //   const endTime = reference.end_ms / 1000;
      //   if (video.currentTime >= endTime) {
      //     video.pause();
      //     setIsPlaying(false);
      //   }
      // }
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false);

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
    };
  }, [reference.source]);

  // 2. 只要 start_ms 变化就跳转
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;
    if (reference.start_ms) {
      const startTime = reference.start_ms / 1000;
      video.currentTime = startTime;
      setCurrentTime(startTime);
    }
  }, [reference.start_ms]);

  // 控制条自动隐藏
  const handleMouseMove = () => {
    setShowControlsVisible(true);
    if (controlsTimeout) clearTimeout(controlsTimeout);
    const timeout = setTimeout(() => {
      if (isPlaying) setShowControlsVisible(false);
    }, 3000);
    setControlsTimeout(timeout);
  };

  // 播放/暂停
  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
  };

  // 进度条点击
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const video = videoRef.current;
    const progressBar = progressRef.current;
    if (!video || !progressBar) return;

    const rect = progressBar.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    video.currentTime = newTime;
  };

  // 音量控制
  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isMuted) {
      video.volume = volume;
      setIsMuted(false);
    } else {
      video.volume = 0;
      setIsMuted(true);
    }
  };

  const handleVolumeChange = (e: React.MouseEvent<HTMLDivElement>) => {
    const video = videoRef.current;
    const volumeBar = volumeRef.current;
    if (!video || !volumeBar) return;

    const rect = volumeBar.getBoundingClientRect();
    const clickY = e.clientY - rect.top;
    const newVolume = 1 - (clickY / rect.height);
    const clampedVolume = Math.max(0, Math.min(1, newVolume));

    video.volume = clampedVolume;
    setVolume(clampedVolume);
    setIsMuted(clampedVolume === 0);
  };

  // 全屏控制
  const toggleFullscreen = () => {
    const container = videoRef.current?.parentElement;
    if (!container) return;

    if (!isFullscreen) {
      if (container.requestFullscreen) {
        container.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  // 快进快退
  const skip = (seconds: number) => {
    const video = videoRef.current;
    if (!video) return;
    video.currentTime = Math.max(0, Math.min(duration, video.currentTime + seconds));
  };

  // 倍速播放
  const changePlaybackRate = (rate: number) => {
    const video = videoRef.current;
    if (!video) return;
    video.playbackRate = rate;
    setPlaybackRate(rate);
    setShowSettings(false);
  };

  // 时间格式化
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 下载视频
  const downloadVideo = () => {
    const link = document.createElement('a');
    link.href = reference.source;
    link.download = 'video.mp4';
    link.click();
  };

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0;
  const volumePercentage = (isMuted ? 0 : volume) * 100;

  return (
    <div
      className="relative w-full bg-black rounded-lg overflow-hidden shadow-lg group"
      onMouseMove={handleMouseMove}
      onMouseLeave={() => isPlaying && setShowControlsVisible(false)}
    >
      {/* 视频元素 */}
      <video
        ref={videoRef}
        src={reference.source}
        className="w-full h-auto object-contain"
        autoPlay={autoPlay}
        onClick={togglePlay}
      />

      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent"></div>
        </div>
      )}

      {/* 中央播放按钮 */}
      {!isPlaying && !isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <button
            onClick={togglePlay}
            className="bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full p-4 transition-all transform hover:scale-110"
            style={{ color: primaryColor }}
          >
            <LuPlay size={32} />
          </button>
        </div>
      )}

      {/* 控制栏 */}
      {showControls && (
        <div
          className={`absolute bottom-0 left-0 right-0 transition-all duration-300 ${showControls ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-full'
            }`}
          style={{
            background: backgroundColor,
            height: controlsHeight
          }}
        >
          {/* 进度条 */}
          <div
            ref={progressRef}
            className="relative h-1 bg-gray-600 cursor-pointer hover:h-2 transition-all"
            onClick={handleProgressClick}
          >
            <div
              className="absolute top-0 left-0 h-full transition-all"
              style={{
                width: `${progressPercentage}%`,
                backgroundColor: primaryColor
              }}
            />
            <div
              className="absolute top-1/2 transform -translate-y-1/2 w-3 h-3 rounded-full transition-all opacity-0 hover:opacity-100"
              style={{
                left: `${progressPercentage}%`,
                backgroundColor: primaryColor,
                marginLeft: '-6px'
              }}
            />
          </div>

          {/* 控制按钮 */}
          <div className="flex items-center justify-between px-4 py-2 mt-2">
            <div className="flex items-center space-x-3">
              {/* 播放/暂停 */}
              <button
                onClick={togglePlay}
                className="text-white hover:text-blue-400 transition-colors"
              >
                {isPlaying ? <LuPause size={20} /> : <LuPlay size={20} />}
              </button>

              {/* 快退 */}
              <button
                onClick={() => skip(-10)}
                className="text-white hover:text-blue-400 transition-colors"
              >
                <LuSkipBack size={18} />
              </button>

              {/* 快进 */}
              <button
                onClick={() => skip(10)}
                className="text-white hover:text-blue-400 transition-colors"
              >
                <LuSkipForward size={18} />
              </button>

              {/* 音量控制 */}
              <div className="relative flex items-center">
                <button
                  onClick={toggleMute}
                  onMouseEnter={() => setShowVolumeSlider(true)}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  {isMuted || volume === 0 ? <LuVolumeX size={20} /> : <LuVolume2 size={20} />}
                </button>

                {/* 音量滑块 */}
                {showVolumeSlider && (
                  <div
                    className="absolute bottom-8 left-0 bg-gray-800 rounded p-2"
                    onMouseLeave={() => setShowVolumeSlider(false)}
                  >
                    <div
                      ref={volumeRef}
                      className="w-1 h-20 bg-gray-600 rounded cursor-pointer relative"
                      onClick={handleVolumeChange}
                    >
                      <div
                        className="absolute bottom-0 left-0 w-full rounded transition-all"
                        style={{
                          height: `${volumePercentage}%`,
                          backgroundColor: primaryColor
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* 时间显示 */}
              <span className="text-white text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            <div className="flex items-center space-x-3">
              {/* 下载按钮 */}
              <button
                onClick={downloadVideo}
                className="text-white hover:text-blue-400 transition-colors"
                title="下载视频"
              >
                <LuDownload size={18} />
              </button>

              {/* 倍速设置 */}
              <div className="relative flex items-center justify-center">
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  <LuSettings size={18} />
                </button>

                {showSettings && (
                  <div className="absolute bottom-8 right-0 bg-gray-800 rounded p-2 min-w-24">
                    <div className="text-white text-xs mb-2">播放速度</div>
                    {[0.5, 0.75, 1, 1.25, 1.5, 2].map((rate) => (
                      <button
                        key={rate}
                        onClick={() => changePlaybackRate(rate)}
                        className={`block w-full text-left px-2 py-1 text-sm rounded transition-colors ${playbackRate === rate
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-300 hover:bg-gray-700'
                          }`}
                      >
                        {rate}x
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* 全屏 */}
              <button
                onClick={toggleFullscreen}
                className="text-white hover:text-blue-400 transition-colors"
              >
                {isFullscreen ? <LuMinimize size={18} /> : <LuMaximize size={18} />}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 时间范围指示器 */}
      {(reference.start_ms || reference.end_ms) && (
        <div className="absolute top-4 left-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded text-sm">
          片段: {reference.start_ms ? formatTime(reference.start_ms / 1000) : '00:00'} - {reference.end_ms ? formatTime(reference.end_ms / 1000) : formatTime(duration)}
        </div>
      )}

      {/* 播放速度指示器 */}
      {playbackRate !== 1 && (
        <div className="absolute top-4 right-4 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
          {playbackRate}x
        </div>
      )}
    </div>
  );
};
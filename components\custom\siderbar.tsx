'use client'

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { IoMdAdd } from "react-icons/io";
import SiderbarItem from './siderbar-item';
import { FaRegMessage } from "react-icons/fa6";
import { TbLayoutSidebarLeftCollapse } from "react-icons/tb";
import { Conversion, useConversionStore } from '@/store/useConversion';
import { shallow } from 'zustand/shallow';
import { cn } from '@/lib/utils';
import { MdOutlineExpandMore, MdOutlineExpandLess } from "react-icons/md";
import { useTranslation } from 'react-i18next';

interface ConversationGroup {
  today: Conversion[];
  yesterday: Conversion[];
  week: Conversion[];
  month: Conversion[];
  older: Conversion[];
}

const Siderbar = () => {

  const { isResponding, isCollapsed, changeCollapsed, conversions, changeConversion, systemConfig } = useConversionStore((state) => ({
    isCollapsed: state.isCollapsed,
    conversions: state.conversions,
    changeConversion: state.changeConversion,
    changeCollapsed: state.changeCollapsed,
    isResponding: state.isResponding,
    systemConfig: state.systemConfig,
  }), shallow)

  const { t } = useTranslation()

  const addNewConversation = useCallback(() => {
    if (isResponding) return
    changeConversion('-1')
  }, [changeConversion, isResponding])

  const [collapsedGroups, setCollapsedGroups] = useState<Record<string, boolean>>({
    today: false,
    yesterday: false,
    week: false,
    month: false,
    older: false
  });

  const toggleGroup = (groupKey: string) => {
    setCollapsedGroups(prev => ({
      ...prev,
      [groupKey]: !prev[groupKey]
    }));
  };

  const groupConversations = (conversations: Conversion[]): ConversationGroup => {
    // 获取今天0点的时间戳
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();

    // 获取昨天0点的时间戳
    const yesterday = new Date(todayTimestamp - 24 * 60 * 60 * 1000);
    const yesterdayTimestamp = yesterday.getTime();

    // 获取一周前0点的时间戳
    const weekAgo = new Date(todayTimestamp - 7 * 24 * 60 * 60 * 1000);
    const weekAgoTimestamp = weekAgo.getTime();

    // 获取一月前0点的时间戳
    const monthAgo = new Date(todayTimestamp - 30 * 24 * 60 * 60 * 1000);
    const monthAgoTimestamp = monthAgo.getTime();

    return conversations.reduce((acc, conv) => {
      // 将对话时间转换为该天0点的时间戳
      const convDate = new Date(conv.created_at);
      convDate.setHours(0, 0, 0, 0);
      const convTimestamp = convDate.getTime();

      if (convTimestamp >= todayTimestamp) {
        acc.today.push(conv);
      } else if (convTimestamp >= yesterdayTimestamp) {
        acc.yesterday.push(conv);
      } else if (convTimestamp >= weekAgoTimestamp) {
        acc.week.push(conv);
      } else if (convTimestamp >= monthAgoTimestamp) {
        acc.month.push(conv);
      } else {
        acc.older.push(conv);
      }

      return acc;
    }, {
      today: [],
      yesterday: [],
      week: [],
      month: [],
      older: []
    } as ConversationGroup);
  };

  const grouped = useMemo(() => groupConversations(conversions), [conversions]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        addNewConversation();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [changeConversion]);

  return (
    <div className={cn('flex flex-col h-full w-full bg-[#F9FBFF] lg:w-[280px]', {
      '!w-0': isCollapsed,
    })}
      style={{
        transition: 'width 0.3s ease-in-out, min-width 0.3s ease-in-out', // 添加min-width过渡
        overflow: 'hidden',
        padding: isCollapsed ? 0 : '1rem',
      }}
    >
      <div className='h-10 flex items-center justify-between'>
        <div className='flex items-center'>
          <img src="/robat.png" alt="" className='w-6 h-6' />
          <span className='ml-2 text-[1.25rem] font-[600] text-[#3B3B39]'>{t('common.sidebar.title')}</span>
        </div>
        <TbLayoutSidebarLeftCollapse
          className='text-2xl cursor-pointer hidden lg:block'
          onClick={() => changeCollapsed()}
        />
      </div>
      <Button onClick={() => addNewConversation()} className='bg-[#DEE9FC] text-[#6076F5] cursor-pointer hover:bg-[#CADBF5] w-full h-10 mt-4 rounded-md flex items-center justify-between'>
        <div className='flex items-center min-w-0 flex-1 mr-2'>
          <IoMdAdd className='text-xl flex-shrink-0' />
          <span className='ml-2 font-bold truncate'>{t('common.sidebar.new_conversation')}</span>
        </div>
        <span className='text-xs text-[rgba(0,87,255,0.3)] space-x-1 hidden lg:flex flex-shrink-0'>
          <span className='border border-[rgba(0,87,255,0.3)] px-1'>Ctrl</span>
          <span className='border border-[rgba(0,87,255,0.3)] px-1'>K</span>
        </span>
      </Button>
      <div className='pt-4 border-t border-[#E0E0E0] mt-4 flex items-center'>
        <div className='mr-2'>
          <FaRegMessage />
        </div>
        <span>{t('common.sidebar.recent_conversation')}</span>
      </div>
      <div className='flex flex-col space-y-2 flex-1 overflow-y-auto no-scrollbar py-4 pl-4'>
        {Object.entries(grouped).map(([key, group]) => {
          if (group.length === 0) return null;
          return (
            <div key={key} className="group">
              <div
                className="text-[.8125rem] font-bold text-[#555] px-2 py-1 uppercase flex items-center justify-between cursor-pointer hover:bg-[#F9FBFF] rounded-md"
                onClick={() => toggleGroup(key)}
              >
                <span className='w-full py-2'>
                  {{
                    today: `🎯 ${t('common.date.today')}`,
                    yesterday: `📅 ${t('common.date.yesterday')}`,
                    week: `📆 ${t('common.date.week')}`,
                    month: `🗓️ ${t('common.date.month')}`,
                    older: `⏳ ${t('common.date.older')}`
                  }[key]}
                </span>
                <span className="text-lg lg:hidden block group-hover:block">
                  {collapsedGroups[key] ? <MdOutlineExpandMore /> : <MdOutlineExpandLess />}
                </span>
              </div>
              {!collapsedGroups[key] && group.map((item: any, index: number) => (
                <SiderbarItem key={index} item={item} index={index} />
              ))}
            </div>
          );
        })}
        {/* {conversions.map((item, index) => (
          <SiderbarItem key={index} item={item} />
        ))} */}
      </div>
    </div>
  );
}

export default Siderbar;

import { fetchAuthCode } from "@/services/api"
import { message } from "antd"

export const webApi = async (username, password) => {
  return new Promise(async (resolve) => {
    try {
      const { data } = await fetchAuthCode('', 'web', username, password);
      const access_token = data.access_token;
      const refresh_token = data.refresh_token;
      localStorage.setItem('access_token', access_token);
      localStorage.setItem('refresh_token', refresh_token);
      resolve(true);
    } catch (error) {
      message.error('获取授权失败');
      resolve(false);
    }
  });
};

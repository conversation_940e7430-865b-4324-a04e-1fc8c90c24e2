import React from 'react'
import { cn } from '@/lib/utils'
import { AiOutlineReload, AiOutlineRight } from "react-icons/ai";
import { useTranslation } from 'react-i18next'
import { useDataSet } from '@/context/data-set';

const QuestionArea = ({ onClick, onDoubleClick }: { onClick: (text: string) => void, onDoubleClick: (text: string) => void }) => {
  const questionIcons = [
    {
      color: '#DD4265',
      icon: 'icon-remen',
    },
    {
      color: '#54A4DB',
      icon: 'icon-iconfont-',
    },
    {
      color: '#F7D367',
      icon: 'icon-xingxing',
    },
    {
      color: '#0000F5',
      icon: 'icon-xiaolian',
    },
    {
      color: '#F1D478',
      icon: 'icon-icon-taikong8',
    },
    {
      color: '#794AD0',
      icon: 'icon-1huojian',
    },
    {
      color: '#F5A623',
      icon: 'icon-a-003-donut',
    },
  ]

  const { questionList, refreshQuestionList } = useDataSet()

  const { t } = useTranslation()

  return (
    <>
      {
        <div className={cn(
          'lg:absolute p-2  lg:w-[790px]  bg-white rounded-md  z-10',
        )}>
          <ul
            className={cn(
              'grid lg:grid-cols-2 grid-cols-1 gap-2 ',
            )}
            style={{ minWidth: '100%' }} // 确保最小宽度和父元素一致
          >
            {questionList.map((question, qIndex) => (
              <li
                key={question.id}
                onClick={() => onClick(question.question)}
                onDoubleClick={() => onDoubleClick(question.question)}
                className='cursor-pointer  lg:h-14 h-12 w-full select-none line-clamp-2 flex items-center justify-between px-3 py-2 rounded-lg bg-white shadow-md hover:bg-[#F5F5F5]'
              >
                <i className={cn('iconfont', questionIcons[qIndex % questionIcons.length].icon)} style={{ color: questionIcons[qIndex % questionIcons.length].color }}></i>
                <span className='flex-1 text-sm text-[#333333] px-2'>{question.question}</span>
                <AiOutlineRight />
              </li>
            ))}
          </ul>
          <div className='mt-4 flex justify-end '>
            <button
              onClick={refreshQuestionList}
              className='flex items-center text-sm hover:text-[#4EACF6] text-[#333333]  cursor-pointer'
            >
              <AiOutlineReload className='mr-1' /> {t('common.refresh')}
            </button>
          </div>
        </div>
      }
    </>
  )
}

export default QuestionArea
